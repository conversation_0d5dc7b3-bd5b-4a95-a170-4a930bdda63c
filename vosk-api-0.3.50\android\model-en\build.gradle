def archiveName = "vosk-model-en"
def pomName = "Vosk English Model"
def pomDescription = "Small English model for Android"

android {
    namespace "org.vosk"
    compileSdkVersion 33
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 10
        versionName = version
        archivesBaseName = archiveName
    }
    buildFeatures {
        buildConfig = false
    }
    sourceSets {
        main {
            assets.srcDirs += "$buildDir/generated/assets"
        }
    }
}

tasks.register('genUUID') {
    def uuid = UUID.randomUUID().toString()
    def odir = file("$buildDir/generated/assets/model-en-us")
    def ofile = file("$odir/uuid")
    doLast {
        mkdir odir
        ofile.text = uuid
    }
}

preBuild.dependsOn(genUUID)

mavenPublishing {
    coordinates("com.alphacephei", archiveName, version)
    pom {
        name = pomName
        description = pomDescription
    }
}
