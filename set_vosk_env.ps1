# Set Vosk Environment Variables
# Run this script as Administrator after fixing the directory structure

Write-Host "Setting Vosk environment variables..." -ForegroundColor Green

$voskRoot = "C:\vosk"

# Check if directories exist
if (Test-Path "$voskRoot\lib") {
    Write-Host "SUCCESS: Found C:\vosk\lib" -ForegroundColor Green
} else {
    Write-Host "ERROR: C:\vosk\lib not found" -ForegroundColor Red
    Write-Host "Please move the lib directory from C:\vosk\vosk-api-0.3.50\lib to C:\vosk\lib" -ForegroundColor Yellow
    exit 1
}

if (Test-Path "$voskRoot\include") {
    Write-Host "SUCCESS: Found C:\vosk\include" -ForegroundColor Green
} else {
    Write-Host "ERROR: C:\vosk\include not found" -ForegroundColor Red
    Write-Host "Please move the include directory from C:\vosk\vosk-api-0.3.50\include to C:\vosk\include" -ForegroundColor Yellow
    exit 1
}

# Set environment variables
Write-Host "Setting environment variables..." -ForegroundColor Cyan

# Set VOSK_ROOT
[Environment]::SetEnvironmentVariable("VOSK_ROOT", $voskRoot, "Machine")
Write-Host "Set VOSK_ROOT = $voskRoot" -ForegroundColor Green

# Set CGO flags
$cgoFlags = "-I$voskRoot\include"
$ldFlags = "-L$voskRoot\lib -lvosk"

[Environment]::SetEnvironmentVariable("CGO_CFLAGS", $cgoFlags, "Machine")
[Environment]::SetEnvironmentVariable("CGO_LDFLAGS", $ldFlags, "Machine")

Write-Host "Set CGO_CFLAGS = $cgoFlags" -ForegroundColor Green
Write-Host "Set CGO_LDFLAGS = $ldFlags" -ForegroundColor Green

# Also add to current session
$env:VOSK_ROOT = $voskRoot
$env:CGO_CFLAGS = $cgoFlags
$env:CGO_LDFLAGS = $ldFlags

Write-Host ""
Write-Host "Environment variables set successfully!" -ForegroundColor Green
Write-Host "Please close and reopen your PowerShell to refresh environment variables." -ForegroundColor Yellow
