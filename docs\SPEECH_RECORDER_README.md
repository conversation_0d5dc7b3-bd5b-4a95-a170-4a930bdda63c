# Go-WISPR Real-time Speech Recorder

A comprehensive real-time speech recording application with automatic microphone detection and speech-to-text transcription capabilities.

## 🎯 Features

### Core Functionality
- **🎤 Automatic Microphone Detection**: Intelligently detects and selects the best available microphone
- **🗣️ Real-time Speech Recording**: Continuous audio capture with live transcription
- **📝 Live Transcription Updates**: Speech-to-text updates every 500ms
- **⌨️ Simple Controls**: Press Enter to stop recording
- **🔧 Verbose Mode**: Detailed device information and troubleshooting

### Device Selection Priority
1. **USB Audio Device** (preferred - as detected by microphone listing tool)
2. **Devices with "microphone" in name**
3. **First available audio input device**

### Cross-Platform Support
- **Windows**: WMI-based device detection with PowerShell integration
- **Linux**: PulseAudio and ALSA support
- **macOS**: system_profiler integration

## 🚀 Quick Start

### Build the Application
```bash
go build -o speech_recorder.exe speech_recorder.go
```

### Basic Usage
```bash
# Start recording with automatic device selection
./speech_recorder.exe

# Start recording with detailed device information
./speech_recorder.exe -verbose
```

### Example Output
```
🎙️  Go-WISPR Real-time Speech Recorder
=========================================
🔍 Detecting audio devices...
✅ Selected device: USB Audio Device
   Description: USB Audio Device
   Status: OK
   USB Audio: true
   Manufacturer: (Generic USB Audio)

🎤 Starting recording with device: USB Audio Device
📝 Transcription will update every 500ms
📋 Press Enter to stop recording...
============================================================
🗣️  Hello world this is a test of the speech recognition system

✅ Recording stopped.
📝 Final transcription: Hello world this is a test of the speech recognition system
👋 Goodbye!
```

## 🏗️ Architecture

### Core Components

#### SpeechRecorder
- Main orchestrator for recording workflow
- Manages device selection and recording lifecycle
- Coordinates audio capture and transcription

#### AudioDevice
- Represents audio input devices with metadata
- Includes device name, description, status, and capabilities
- Supports USB Audio device identification

#### VoskRecognizer
- Speech-to-text engine integration
- Model loading and audio processing
- Real-time transcription capabilities

#### AudioProcessor
- Audio capture and processing pipeline
- Noise reduction and voice activity detection
- Audio buffer management

### Key Features

#### Device Detection
```go
// Automatic device detection across platforms
devices, err := recorder.DetectAudioDevices()
selectedDevice := recorder.SelectDevice(devices)
```

#### Real-time Transcription
```go
// 500ms update intervals for live transcription
updateInterval: 500 * time.Millisecond
```

#### Concurrent Processing
- Audio capture goroutine
- Transcription update goroutine
- Speech recognition processing
- User input handling

## 🧪 Testing

### Run the Test Suite
```bash
go test -v speech_recorder.go speech_recorder_test.go
```

### Test Coverage
- **Unit Tests**: Device detection, selection logic, recording lifecycle
- **Integration Tests**: Complete recording workflow
- **Concurrent Access Tests**: Thread-safety validation
- **Performance Tests**: Update interval timing, device detection benchmarks
- **Error Handling Tests**: Various failure scenarios

### Test Categories

#### Device Detection Tests
- Windows WMI integration
- Linux PulseAudio/ALSA support
- macOS system_profiler integration
- Cross-platform compatibility

#### Recording Lifecycle Tests
- Start/stop recording functionality
- Transcription buffer management
- Context cancellation
- Resource cleanup

#### Performance Tests
- Device detection benchmarks
- Transcription update performance
- Memory usage optimization
- Real-time processing validation

## 🔧 Technical Specifications

### Audio Processing
- **Sample Rate**: 16 kHz (optimized for speech recognition)
- **Buffer Size**: 4096 samples
- **Format**: 16-bit PCM
- **Channels**: Mono (single channel)

### Speech Recognition
- **Engine**: Vosk (with fallback simulation)
- **Model**: English small model (vosk-model-small-en-us-0.15)
- **Update Frequency**: 500ms intervals
- **Processing**: Real-time partial and final results

### System Requirements
- **Go**: Version 1.23.1 or later
- **Memory**: Minimum 512MB available RAM
- **Storage**: 100MB for Vosk models
- **Audio**: Working microphone/audio input device

## 🛠️ Configuration

### Model Path Configuration
```go
// Default Vosk model path
modelPath := "models/vosk/english-small/vosk-model-small-en-us-0.15"
```

### Update Interval Customization
```go
// Customize transcription update frequency
updateInterval: 250 * time.Millisecond  // Faster updates
updateInterval: 1000 * time.Millisecond // Slower updates
```

### Device Selection Override
```go
// Manual device selection
recorder.selectedDevice = &AudioDevice{
    Name: "Custom Microphone",
    Description: "Custom Audio Device",
    Status: "OK",
}
```

## 🔍 Troubleshooting

### Common Issues

#### No Microphones Detected
1. Check Windows Sound Settings > Recording tab
2. Ensure microphone drivers are installed
3. Verify microphone privacy permissions
4. Run application as Administrator

#### Audio Capture Issues
1. Verify microphone is not in use by other applications
2. Check audio device status in Device Manager
3. Test microphone with Windows Sound Recorder
4. Ensure proper audio levels

#### Speech Recognition Issues
1. Verify Vosk model files are present
2. Check model path configuration
3. Ensure sufficient system memory
4. Validate audio input quality

### Verbose Mode Debugging
```bash
# Get detailed device and processing information
./speech_recorder.exe -verbose
```

## 🚧 Future Enhancements

### Planned Features
- **Real Vosk Integration**: Complete speech-to-text implementation
- **Multiple Model Support**: Whisper, cloud APIs, custom models
- **Audio Preprocessing**: Noise reduction, echo cancellation
- **Configuration File**: Persistent settings and preferences
- **Recording Export**: Save audio and transcriptions
- **Language Support**: Multi-language speech recognition

### Performance Optimizations
- **GPU Acceleration**: CUDA/OpenCL support for faster processing
- **Streaming Processing**: Reduced latency audio pipeline
- **Memory Optimization**: Efficient buffer management
- **Batch Processing**: Optimized transcription algorithms

## 📝 Integration with Go-WISPR

This speech recorder integrates seamlessly with the Go-WISPR voice dictation project:

1. **Microphone Detection**: Uses the same device detection logic as `list_mics.go`
2. **Audio Processing**: Foundation for voice command capture
3. **Speech Recognition**: Core STT functionality for dictation
4. **Real-time Processing**: Live transcription for immediate feedback

## 📄 License

Part of the Go-WISPR project - A voice dictation tool built in Go.
