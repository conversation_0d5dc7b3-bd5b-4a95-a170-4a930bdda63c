package main

import (
	"flag"
	"fmt"
	"log"
	"os/exec"
	"runtime"
	"strings"
)

func listMicrophones(verbose bool) error {
	switch runtime.GOOS {
	case "windows":
		return listMicrophonesWindows(verbose)
	case "linux":
		return listMicrophonesLinux(verbose)
	case "darwin":
		return listMicrophonesMacOS(verbose)
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

func listMicrophonesWindows(verbose bool) error {
	fmt.Println("Listing microphones on Windows...")

	found := false

	// Primary Method: WMI Win32_SoundDevice - Focus on microphone devices
	fmt.Println("\nDetecting microphones using Windows Management Instrumentation...")

	cmd := exec.Command("powershell", "-Command", `
		$devices = Get-WmiObject -Class Win32_SoundDevice | Where-Object {$_.Status -eq "OK"}
		$micDevices = @()

		foreach ($device in $devices) {
			# Check if device name or description suggests it's a microphone/input device
			$isMic = $false
			$deviceName = $device.Name.ToLower()
			$deviceDesc = $device.Description.ToLower()

			# Look for microphone indicators
			if ($deviceName -match "microphone|mic|audio|usb.*audio|condenser|capture|recording|input" -or
				$deviceDesc -match "microphone|mic|audio|usb.*audio|condenser|capture|recording|input") {
				$isMic = $true
			}

			# Special handling for USB Audio devices (commonly microphones)
			if ($deviceName -match "usb.*audio" -or $deviceDesc -match "usb.*audio") {
				$isMic = $true
			}

			if ($isMic) {
				$micDevices += [PSCustomObject]@{
					Name = $device.Name
					Description = $device.Description
					Status = $device.Status
					Manufacturer = $device.Manufacturer
					DeviceType = "Microphone/Audio Input"
				}
			}
		}

		if ($micDevices.Count -gt 0) {
			Write-Output "MICROPHONES_FOUND"
			$micDevices | Format-Table -AutoSize Name, Description, Status, Manufacturer, DeviceType
		} else {
			Write-Output "NO_MICROPHONES_FOUND"
			if ($devices.Count -gt 0) {
				Write-Output "ALL_AUDIO_DEVICES:"
				$devices | Format-Table -AutoSize Name, Description, Status, Manufacturer
			}
		}
	`)

	if output, err := cmd.Output(); err == nil {
		result := strings.TrimSpace(string(output))
		if result != "" {
			if strings.Contains(result, "MICROPHONES_FOUND") {
				// Extract and display microphone devices
				lines := strings.Split(result, "\n")
				microphoneSection := false
				fmt.Println("\n🎤 Detected Microphone Devices:")
				fmt.Println("=" + strings.Repeat("=", 50))

				for _, line := range lines {
					if strings.Contains(line, "MICROPHONES_FOUND") {
						microphoneSection = true
						continue
					}
					if microphoneSection && strings.TrimSpace(line) != "" {
						fmt.Println(line)
					}
				}
				found = true
			} else if strings.Contains(result, "NO_MICROPHONES_FOUND") {
				fmt.Println("\n⚠️  No dedicated microphone devices detected via primary method.")

				if strings.Contains(result, "ALL_AUDIO_DEVICES:") {
					fmt.Println("\n📋 All Available Audio Devices:")
					fmt.Println("=" + strings.Repeat("=", 40))
					lines := strings.Split(result, "\n")
					audioSection := false

					for _, line := range lines {
						if strings.Contains(line, "ALL_AUDIO_DEVICES:") {
							audioSection = true
							continue
						}
						if audioSection && strings.TrimSpace(line) != "" {
							fmt.Println(line)
						}
					}
					fmt.Println("\n💡 Note: Some of these devices may support microphone input.")
					fmt.Println("   Check Windows Sound Settings > Recording tab for enabled microphones.")
				}
			}
		}
	} else {
		if verbose {
			fmt.Printf("Error running WMI query: %v\n", err)
		}
		fmt.Println("⚠️  Could not access Windows audio device information.")
	}

	// Additional verbose information and fallback guidance
	if verbose && found {
		fmt.Println("\n🔧 Additional Information:")
		fmt.Println("  - Devices shown above are detected by Windows WMI")
		fmt.Println("  - Status 'OK' means the device is properly installed")
		fmt.Println("  - USB Audio devices often include microphone functionality")
		fmt.Println("  - Check Windows Sound Settings > Recording tab to enable/configure")
	}

	if !found {
		fmt.Println("\n❌ No microphones detected via automated detection.")
		fmt.Println("\n📋 Manual Check Instructions:")
		fmt.Println("  1. Press Win + R, type 'mmsys.cpl' and press Enter")
		fmt.Println("  2. Click on the 'Recording' tab")
		fmt.Println("  3. Look for microphone devices (they may be disabled)")
		fmt.Println("  4. Right-click in empty space and select 'Show Disabled Devices'")
		fmt.Println("  5. Right-click on your microphone and select 'Enable' if needed")

		if verbose {
			fmt.Println("\n🔍 Troubleshooting Tips:")
			fmt.Println("  - Ensure microphone drivers are installed")
			fmt.Println("  - Check Device Manager for audio input devices")
			fmt.Println("  - Verify microphone privacy settings in Windows 10/11")
			fmt.Println("  - Test microphone in Windows Sound Recorder app")
			fmt.Println("  - Try running this tool as Administrator")
		}
	} else {
		fmt.Println("\n✅ Microphone detection completed successfully!")
		if verbose {
			fmt.Println("\n💡 Next Steps:")
			fmt.Println("  - Test your microphone in Windows Sound Recorder")
			fmt.Println("  - Adjust microphone levels in Sound Settings if needed")
			fmt.Println("  - Ensure microphone privacy permissions are enabled")
		}
	}

	return nil
}

func listMicrophonesLinux(verbose bool) error {
	fmt.Println("Listing microphones on Linux...")

	// Try different Linux audio systems
	commands := []struct {
		name string
		cmd  []string
		desc string
	}{
		{"PulseAudio", []string{"pactl", "list", "short", "sources"}, "PulseAudio sources"},
		{"ALSA", []string{"arecord", "-l"}, "ALSA recording devices"},
	}

	found := false
	for _, cmdInfo := range commands {
		cmd := exec.Command(cmdInfo.cmd[0], cmdInfo.cmd[1:]...)
		output, err := cmd.Output()
		if err == nil {
			found = true
			fmt.Printf("\n%s (%s):\n", cmdInfo.name, cmdInfo.desc)
			fmt.Println(strings.TrimSpace(string(output)))
		} else if verbose {
			fmt.Printf("Note: %s not available (%v)\n", cmdInfo.name, err)
		}
	}

	if !found {
		fmt.Println("Could not detect audio system. Please install PulseAudio or ALSA tools.")
	}

	return nil
}

func listMicrophonesMacOS(verbose bool) error {
	fmt.Println("Listing microphones on macOS...")

	// Use system_profiler to list audio devices
	cmd := exec.Command("system_profiler", "SPAudioDataType")
	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("Error running system_profiler: %v\n", err)
		fmt.Println("Tip: Check System Preferences > Sound > Input for available microphones")
		return nil
	}

	result := string(output)
	lines := strings.Split(result, "\n")

	fmt.Println("\nAudio Devices:")
	inInputSection := false
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "Input") {
			inInputSection = true
		}
		if inInputSection && line != "" {
			fmt.Printf("  %s\n", line)
		}
		if line == "" && inInputSection {
			inInputSection = false
		}
	}

	return nil
}

func main() {
	// Parse command line flags
	verbose := flag.Bool("verbose", false, "Show detailed information about microphones")
	flag.Parse()

	fmt.Println("Go-WISPR Microphone Listing Tool")
	fmt.Println("=================================")
	fmt.Println()

	if err := listMicrophones(*verbose); err != nil {
		log.Fatalf("Error listing microphones: %v", err)
	}
}
