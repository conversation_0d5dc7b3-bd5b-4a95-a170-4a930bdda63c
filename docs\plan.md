## 1. Project Overview

A Go-based, cross-platform (Windows/macOS) dictation app that:

* **Real-time Speech→Text** (dictation) with sub-500 ms latency.
* **On-device Translation** to a chosen target language, sub-200 ms latency.
* **Local Models Only** for privacy and offline operation.
* **User-tunable** speed vs. accuracy via model selection.

---

## 2. Core Architecture

```
┌───────────────────┐     ┌───────────────────────┐
│ Audio Processing  │──►  │ Speech-to-Text Engine │
│  (malgo/VAD,      │     │ (Vosk / Whisper.cpp)  │
│   Noise Reduction)│     └───────────────────────┘
└───────────────────┘              │
                                   ▼
                           ┌───────────────────┐
                           │ Translation Layer │
                           │ (ONNX MarianMT /  │
                           │  CTranslate2 /    │
                           │  LLaMA via llama) │
                           └───────────────────┘
                                   │
                                   ▼
┌───────────────────┐     ┌───────────────────┐     ┌───────────────────┐
│  Model Management │◄───►│   Settings & UX   │◄───►│    UI Layer       │
│ (Load/Switch/Caching)│   │ (Language, Models)│     │ (Fyne / Wails)    │
└───────────────────┘     └───────────────────┘     └───────────────────┘
```

---

## 3. Technology Stack & Trade-Offs

### 3.1 UI Framework

* **Fyne** (Go native) — fastest startup, single binary, true native widgets.
* **Wails** (Go + WebView) — richer styling but extra WebView overhead.

> **Choose <PERSON>yne** for maximal responsiveness.

### 3.2 STT Engines

| Engine          | Models                    | Latency\* | Accuracy        | Footprint   |
| --------------- | ------------------------- | --------- | --------------- | ----------- |
| **Vosk**        | 50 MB / language          | 50–200 ms | Moderate        | Very small  |
| **Whisper.cpp** | tiny→large (39 MB→1.5 GB) | 32×→1× RT | High (tiny→max) | Small→Large |

> \*Measured on mid-range CPU. RT = real-time factor (RTF < 1).

**Recommendation:**

* **Default "Speed Mode"** → Vosk small model.
* **"Accuracy Mode"** → Whisper.cpp base/small.

### 3.3 Translation Engines

| Engine                      | Format              | Latency      | Quality   | Integration    |
| --------------------------- | ------------------- | ------------ | --------- | -------------- |
| **MarianMT / mBART (ONNX)** | ONNX + INT8/FP16    | 100–300 ms   | Good      | onnxruntime-go |
| **CTranslate2**             | custom .ctranslate2 | 50–200 ms    | High      | cgo shim       |
| **LLaMA (llama.cpp)**       | GGML                | 300–1 000 ms | Very high | go-llama.cpp   |

> Offer ONNX as default; allow power users to opt into CTranslate2 or LLaMA.

---

## 4. Speed Optimization Strategies

* **Streaming vs. Batch**

  * **Streaming** for instant interim feedback (100–500 ms windows).
  * **Batch "Final" Pass** at end of sentence for highest accuracy.

* **Quantization**

  * INT8 or FP16 for ONNX to cut inference time \~2×.
  * Whisper.cpp & llama.cpp support 4-bit quant on compatible hardware.

* **Concurrency**

  * Dedicated goroutines for capture, STT, translation, and UI updates.
  * Bounded channels to smooth bursts.

* **Hardware Accel**

  * Detect CUDA (Windows) or Metal (macOS) for ONNX EPs (XNNPACK/Metal).
  * Fallback to CPU if unavailable.

---

## 5. Detailed Implementation Roadmap

| Phase                 | Tasks                                                                           | Duration |
| --------------------- | ------------------------------------------------------------------------------- | -------- |
| **1. Core**           | • Init Go modules & deps (malgo, Fyne, Vosk, whisper.cpp, onnxruntime-go)       | 1 wk     |
|                       | • Audio capture + VAD + noise reduction                                         |          |
| **2. STT**            | • Integrate Vosk small & Whisper.cpp tiny/base                                  | 1 wk     |
|                       | • Build streaming pipeline + interim/final text                                 |          |
| **3. Translation**    | • Hook ONNX MarianMT; auto language detection; caching strategies               | 1 wk     |
| **4. UI & Settings**  | • Fyne UI: start/stop, transcript & translation panes, settings dialog          | 1 wk     |
|                       | • Settings: engine toggles, model download manager, language pickers            |          |
| **5. Optimization**   | • Quantize models; tune chunk sizes; GPU EP integration                         | 1 wk     |
| **6. Comparison**     | • Build "benchmark" mode: side-by-side engine tests with metrics dashboard      | 1 wk     |
| **7. Packaging & QA** | • Cross-compile via fyne-cross; create DMG & MSI installers; end-to-end testing | 1 wk     |

---

## 6. Performance Comparison Framework

### 6.1 Metrics

* **Latency**

  * STT interim & final (ms & RTF)
  * Translation per sentence (ms)
  * End-to-end (audio in → text out)
* **Accuracy**

  * WER (Word Error Rate) on standard speech corpora
  * BLEU/COMET on translation test sets
* **Resource**

  * Memory footprint per model
  * Peak CPU & GPU utilization
  * Power draw (battery impact)

### 6.2 User Dashboard

* **Real-time graphs** of latency & confidence scores.
* **Side-by-side** panes: dictate same phrase under different engines.
* **History log** of benchmarks to inform model choices.

---

## 7. Cross-Platform Considerations

### Windows

* **WASAPI** via malgo for pro-audio quality.
* **CUDA** for ONNX EP if NVIDIA GPU present.
* Inno Setup / WiX for MSI installer.

### macOS

* **Core Audio** backend via malgo for minimal jitter.
* **Metal** EP for ONNX on Apple Silicon.
* Code-sign & notarize; bundle DMG with `.app` sandbox.

---

## 8. Model Selection Strategy

| Scenario        | STT Engine & Model       | Translation Engine          |
| --------------- | ------------------------ | --------------------------- |
| **Max Speed**   | Vosk small               | MarianMT ONNX, INT8         |
| **Balanced**    | Whisper.cpp base/small   | mBART/mT5 ONNX, FP16        |
| **Max Quality** | Whisper.cpp medium/large | LLaMA via llama.cpp (4-bit) |

Allow per-language "profiles" so user can pre-select best combo.

---

## 9. Success Metrics & Targets

* **Latency**

  * STT interim < 300 ms; final < 500 ms
  * Translation < 200 ms per utterance
* **Accuracy**

  * WER < 5% for clear speech
  * BLEU ≥ 0.8 on common language pairs
* **UX**

  * App launch < 1 s
  * Model swap < 2 s
  * Real-time feedback with no UI freezes
