# Vosk CGO Dependencies Installation Guide for Windows

## 🎯 Overview

This guide will help you install the necessary CGO dependencies to get Vosk speech recognition working with your Go application on Windows.

## 📋 Prerequisites

- Windows 10/11
- Go 1.19+ installed
- Administrator access
- Internet connection

## 🚀 Quick Installation (Automated)

### Option 1: Run the PowerShell Script

1. **Open PowerShell as Administrator**
2. **Navigate to your project directory**:
   ```powershell
   cd d:\_work\_me\sam-wispr
   ```
3. **Run the installation script**:
   ```powershell
   .\install_vosk_dependencies.ps1
   ```

This script will automatically:
- Install Chocolatey (package manager)
- Install MinGW-w64 (GCC compiler)
- Download Vosk C libraries
- Set environment variables
- Verify the installation

## 🔧 Manual Installation

### Step 1: Install a C Compiler

Choose one of these options:

#### Option A: TDM-GCC (Recommended for beginners)
1. Download from: https://jmeubank.github.io/tdm-gcc/
2. Run the installer
3. Follow the installation wizard
4. Verify: `gcc --version`

#### Option B: MinGW-w64
1. Download from: https://www.mingw-w64.org/downloads/
2. Choose MSYS2 installer
3. Follow installation guide
4. Add bin directory to PATH

#### Option C: Chocolatey
```powershell
# Install Chocolatey first (if not installed)
Set-ExecutionPolicy Bypass -Scope Process -Force
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install MinGW
choco install mingw -y
```

### Step 2: Download Vosk C Libraries

1. **Download**: Go to https://github.com/alphacep/vosk-api/releases
2. **Choose**: `vosk-win64-0.3.50.zip` (or latest version)
3. **Extract**: To `C:\vosk` (or your preferred location)

### Step 3: Set Environment Variables

```powershell
# Set VOSK_ROOT
$env:VOSK_ROOT = "C:\vosk"
[Environment]::SetEnvironmentVariable("VOSK_ROOT", "C:\vosk", "Machine")

# Set CGO flags
$env:CGO_CFLAGS = "-IC:\vosk\include"
$env:CGO_LDFLAGS = "-LC:\vosk\lib -lvosk"
[Environment]::SetEnvironmentVariable("CGO_CFLAGS", "-IC:\vosk\include", "Machine")
[Environment]::SetEnvironmentVariable("CGO_LDFLAGS", "-LC:\vosk\lib -lvosk", "Machine")
```

### Step 4: Update Go Dependencies

1. **Clean up broken dependencies**:
   ```bash
   go mod edit -droprequire github.com/ggerganov/whisper.cpp/bindings/go
   rm go.sum
   ```

2. **Add correct Vosk dependency**:
   ```bash
   go get github.com/alphacep/vosk-api/go@v0.3.50
   go mod tidy
   ```

## 🧪 Testing the Installation

### Test 1: Build the Corrected Vosk Version
```bash
go build speech_recorder_vosk_corrected.go
```

### Test 2: Run the Installation Test
```bash
go run test_vosk_installation.go
```

### Test 3: Verify Environment
```powershell
# Check GCC
gcc --version

# Check environment variables
echo $env:VOSK_ROOT
echo $env:CGO_CFLAGS
echo $env:CGO_LDFLAGS

# Check Vosk files
ls C:\vosk\lib\vosk.lib
ls C:\vosk\include\vosk_api.h
```

## 📁 File Structure

After installation, you should have:

```
d:\_work\_me\sam-wispr\
├── speech_recorder.go                 # Current working version (mock)
├── speech_recorder_vosk_corrected.go  # Real Vosk integration
├── test_vosk_installation.go          # Installation test
├── install_vosk_dependencies.ps1      # Automated installer
├── go.mod                             # Updated with Vosk dependency
├── models/
│   └── vosk/
│       └── english-small/
│           └── vosk-model-small-en-us-0.15/  # Vosk model files
└── C:\vosk\                           # Vosk C libraries
    ├── lib\
    │   └── vosk.lib
    └── include\
        └── vosk_api.h
```

## 🔍 Troubleshooting

### Common Issues

#### 1. "gcc: command not found"
- **Solution**: Install GCC compiler (see Step 1)
- **Verify**: `gcc --version`

#### 2. "undefined reference to vosk functions"
- **Solution**: Check CGO_LDFLAGS environment variable
- **Verify**: `echo $env:CGO_LDFLAGS`

#### 3. "vosk.h: No such file or directory"
- **Solution**: Check CGO_CFLAGS environment variable
- **Verify**: `echo $env:CGO_CFLAGS`

#### 4. "module does not contain package"
- **Solution**: Use correct import path: `github.com/alphacep/vosk-api/go`
- **Not**: `github.com/alphacep/vosk-api/go/vosk`

#### 5. Build takes very long or hangs
- **Solution**: CGO compilation can be slow on first build
- **Wait**: 5-10 minutes for first build

### Environment Variables Check

```powershell
# Check all required variables
Get-ChildItem Env: | Where-Object {$_.Name -match "VOSK|CGO"}
```

### Clean Build

```bash
# Clean Go module cache
go clean -modcache

# Clean build cache
go clean -cache

# Rebuild
go build -v speech_recorder_vosk_corrected.go
```

## 🎉 Success Indicators

You'll know the installation is successful when:

1. ✅ `gcc --version` shows compiler version
2. ✅ Environment variables are set correctly
3. ✅ `go build speech_recorder_vosk_corrected.go` compiles without errors
4. ✅ `test_vosk_installation.go` runs successfully
5. ✅ Vosk model loads without errors

## 📞 Getting Help

If you encounter issues:

1. **Check the terminal output** for specific error messages
2. **Verify all environment variables** are set correctly
3. **Ensure Vosk model files** are downloaded and in the correct location
4. **Try a clean build** after clearing caches
5. **Check Windows Defender/Antivirus** isn't blocking files

## 🔄 Next Steps

Once installation is complete:

1. **Test the corrected Vosk version**: `speech_recorder_vosk_corrected.go`
2. **Download additional Vosk models** if needed
3. **Implement real audio capture** (replace mock with actual microphone input)
4. **Add comprehensive tests** for the speech recognition functionality

## 📚 Additional Resources

- [Vosk Official Documentation](https://alphacephei.com/vosk/)
- [Vosk Models Download](https://alphacephei.com/vosk/models)
- [Go CGO Documentation](https://golang.org/cmd/cgo/)
- [MinGW-w64 Documentation](https://www.mingw-w64.org/)
