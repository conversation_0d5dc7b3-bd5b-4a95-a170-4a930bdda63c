# Microphone Listing CLI Tool

A cross-platform command-line application that lists available microphones capable of capturing voice commands.

## Features

- **Cross-platform support**: Works on Windows, Linux, and macOS
- **Simple CLI interface**: Easy to use command-line tool
- **Verbose mode**: Detailed information about microphones and system audio
- **No external dependencies**: Uses system commands for audio device detection

## Installation

1. Ensure you have Go installed (version 1.23.1 or later)
2. Build the application:
   ```bash
   go build -o list_mics.exe list_mics.go
   ```

## Usage

### Basic Usage
```bash
./list_mics.exe
```

### Verbose Mode
```bash
./list_mics.exe -verbose
```

## Platform-Specific Behavior

### Windows
- Uses PowerShell WMI queries to detect audio devices
- Provides guidance to check Windows Sound Settings
- Fallback instructions for manual verification

### Linux
- Attempts to use PulseAudio (`pactl`) for device listing
- Falls back to ALSA (`arecord`) if PulseAudio is not available
- Provides installation guidance for missing audio tools

### macOS
- Uses `system_profiler` to enumerate audio devices
- Filters for input devices specifically
- Provides guidance to check System Preferences

## Example Output

```
Go-WISPR Microphone Listing Tool
=================================

Listing microphones on Windows...
No microphones detected via WMI query.

Tip: Check Windows Sound Settings > Recording tab for available microphones
```

### Verbose Output
```
Go-WISPR Microphone Listing Tool
=================================

Listing microphones on Windows...
No microphones detected via WMI query.

Tip: Check Windows Sound Settings > Recording tab for available microphones

Additional Information:
  - Run 'mmsys.cpl' to open Sound settings
  - Check 'Recording' tab for microphone devices
  - Ensure microphones are enabled and not muted
```

## Integration with Go-WISPR

This tool is part of the Go-WISPR voice dictation project and can be used to:

1. **Verify microphone availability** before starting voice capture
2. **Troubleshoot audio issues** in the main application
3. **Provide user guidance** for audio setup

## Technical Notes

- Uses Go's `os/exec` package to run system commands
- Handles errors gracefully with fallback messages
- Designed to work without CGO dependencies
- Cross-platform detection using `runtime.GOOS`

## Future Enhancements

- Integration with malgo/portaudio for direct audio device access
- Real-time microphone testing capabilities
- Audio level monitoring
- Device capability detection (sample rates, channels, etc.)
