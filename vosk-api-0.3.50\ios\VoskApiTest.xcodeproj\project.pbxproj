// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		92375222240C550B00DD6076 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 92375221240C550B00DD6076 /* AppDelegate.swift */; };
		92375224240C550B00DD6076 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 92375223240C550B00DD6076 /* ViewController.swift */; };
		92375227240C550B00DD6076 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 92375225240C550B00DD6076 /* Main.storyboard */; };
		92375229240C550B00DD6076 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 92375228240C550B00DD6076 /* Assets.xcassets */; };
		9237522C240C550B00DD6076 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 9237522A240C550B00DD6076 /* LaunchScreen.storyboard */; };
		92375234240C558900DD6076 /* Vosk.swift in Sources */ = {isa = PBXBuildFile; fileRef = 92375233240C558900DD6076 /* Vosk.swift */; };
		92375244240C6DAF00DD6076 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 92375243240C6DAF00DD6076 /* Accelerate.framework */; };
		92375274240C6F1E00DD6076 /* 10001-90210-01803.wav in Resources */ = {isa = PBXBuildFile; fileRef = 92375256240C6E3D00DD6076 /* 10001-90210-01803.wav */; };
		925527A9273C492C00FFD9CC /* libvosk.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 925527A8273C492C00FFD9CC /* libvosk.a */; };
		92833003273C466E00058B52 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 92833002273C466E00058B52 /* libc++.tbd */; };
		92BACED125BE125A00B5CC93 /* vosk-model-small-en-us-0.15 in Resources */ = {isa = PBXBuildFile; fileRef = 928CC50C25BE124400490481 /* vosk-model-small-en-us-0.15 */; };
		92D6B8D325BDFEAC007FF08D /* VoskModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 92D6B8D225BDFEAC007FF08D /* VoskModel.swift */; };
		92D86BD6253F823F0040D53F /* vosk-model-spk-0.4 in Resources */ = {isa = PBXBuildFile; fileRef = 92D86BD4253F823F0040D53F /* vosk-model-spk-0.4 */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		9237521E240C550B00DD6076 /* VoskApiTest.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = VoskApiTest.app; sourceTree = BUILT_PRODUCTS_DIR; };
		92375221240C550B00DD6076 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		92375223240C550B00DD6076 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		92375226240C550B00DD6076 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		92375228240C550B00DD6076 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		9237522B240C550B00DD6076 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		9237522D240C550B00DD6076 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		92375233240C558900DD6076 /* Vosk.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Vosk.swift; sourceTree = "<group>"; };
		92375243240C6DAF00DD6076 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		92375256240C6E3D00DD6076 /* 10001-90210-01803.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = "10001-90210-01803.wav"; sourceTree = "<group>"; };
		925527A8273C492C00FFD9CC /* libvosk.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libvosk.a; sourceTree = "<group>"; };
		92833002273C466E00058B52 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		928CC50C25BE124400490481 /* vosk-model-small-en-us-0.15 */ = {isa = PBXFileReference; lastKnownFileType = folder; name = "vosk-model-small-en-us-0.15"; path = "/Users/<USER>/Documents/IOS/VoskApiTest/VoskApiTest/Vosk/vosk-model-small-en-us-0.15"; sourceTree = "<absolute>"; };
		92AA22AD244CDD1200DA464B /* vosk_api.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = vosk_api.h; sourceTree = "<group>"; };
		92AA22AE244CDD5200DA464B /* bridging.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = bridging.h; sourceTree = "<group>"; };
		92D6B8D225BDFEAC007FF08D /* VoskModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoskModel.swift; sourceTree = "<group>"; };
		92D86BD4253F823F0040D53F /* vosk-model-spk-0.4 */ = {isa = PBXFileReference; lastKnownFileType = folder; path = "vosk-model-spk-0.4"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		9237521B240C550B00DD6076 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				92833003273C466E00058B52 /* libc++.tbd in Frameworks */,
				92375244240C6DAF00DD6076 /* Accelerate.framework in Frameworks */,
				925527A9273C492C00FFD9CC /* libvosk.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		92375215240C550A00DD6076 = {
			isa = PBXGroup;
			children = (
				92375239240C642000DD6076 /* Vosk */,
				92375220240C550B00DD6076 /* VoskApiTest */,
				9237521F240C550B00DD6076 /* Products */,
				92375242240C6DAF00DD6076 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		9237521F240C550B00DD6076 /* Products */ = {
			isa = PBXGroup;
			children = (
				9237521E240C550B00DD6076 /* VoskApiTest.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		92375220240C550B00DD6076 /* VoskApiTest */ = {
			isa = PBXGroup;
			children = (
				92AA22AE244CDD5200DA464B /* bridging.h */,
				92375221240C550B00DD6076 /* AppDelegate.swift */,
				92375223240C550B00DD6076 /* ViewController.swift */,
				92375225240C550B00DD6076 /* Main.storyboard */,
				92375228240C550B00DD6076 /* Assets.xcassets */,
				9237522A240C550B00DD6076 /* LaunchScreen.storyboard */,
				9237522D240C550B00DD6076 /* Info.plist */,
				92375233240C558900DD6076 /* Vosk.swift */,
				92D6B8D225BDFEAC007FF08D /* VoskModel.swift */,
			);
			path = VoskApiTest;
			sourceTree = "<group>";
		};
		92375239240C642000DD6076 /* Vosk */ = {
			isa = PBXGroup;
			children = (
				92D86BD4253F823F0040D53F /* vosk-model-spk-0.4 */,
				928CC50C25BE124400490481 /* vosk-model-small-en-us-0.15 */,
				925527A8273C492C00FFD9CC /* libvosk.a */,
				92AA22AD244CDD1200DA464B /* vosk_api.h */,
				92375256240C6E3D00DD6076 /* 10001-90210-01803.wav */,
			);
			name = Vosk;
			path = VoskApiTest/Vosk;
			sourceTree = "<group>";
		};
		92375242240C6DAF00DD6076 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				92833002273C466E00058B52 /* libc++.tbd */,
				92375243240C6DAF00DD6076 /* Accelerate.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		9237521D240C550B00DD6076 /* VoskApiTest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 92375230240C550B00DD6076 /* Build configuration list for PBXNativeTarget "VoskApiTest" */;
			buildPhases = (
				9237521A240C550B00DD6076 /* Sources */,
				9237521B240C550B00DD6076 /* Frameworks */,
				9237521C240C550B00DD6076 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = VoskApiTest;
			productName = VoskApiTest;
			productReference = 9237521E240C550B00DD6076 /* VoskApiTest.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		92375216240C550A00DD6076 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 0830;
				LastUpgradeCheck = 0920;
				ORGANIZATIONNAME = "Alpha Cephei";
				TargetAttributes = {
					9237521D240C550B00DD6076 = {
						CreatedOnToolsVersion = 8.3.2;
						LastSwiftMigration = 0920;
						ProvisioningStyle = Manual;
					};
				};
			};
			buildConfigurationList = 92375219240C550A00DD6076 /* Build configuration list for PBXProject "VoskApiTest" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 92375215240C550A00DD6076;
			productRefGroup = 9237521F240C550B00DD6076 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				9237521D240C550B00DD6076 /* VoskApiTest */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		9237521C240C550B00DD6076 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				92BACED125BE125A00B5CC93 /* vosk-model-small-en-us-0.15 in Resources */,
				92375274240C6F1E00DD6076 /* 10001-90210-01803.wav in Resources */,
				9237522C240C550B00DD6076 /* LaunchScreen.storyboard in Resources */,
				92375229240C550B00DD6076 /* Assets.xcassets in Resources */,
				92D86BD6253F823F0040D53F /* vosk-model-spk-0.4 in Resources */,
				92375227240C550B00DD6076 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		9237521A240C550B00DD6076 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				92375224240C550B00DD6076 /* ViewController.swift in Sources */,
				92375222240C550B00DD6076 /* AppDelegate.swift in Sources */,
				92D6B8D325BDFEAC007FF08D /* VoskModel.swift in Sources */,
				92375234240C558900DD6076 /* Vosk.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		92375225240C550B00DD6076 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				92375226240C550B00DD6076 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		9237522A240C550B00DD6076 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				9237522B240C550B00DD6076 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		9237522E240C550B00DD6076 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.3;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OBJC_BRIDGING_HEADER = bridging.h;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		9237522F240C550B00DD6076 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.3;
				MTL_ENABLE_DEBUG_INFO = NO;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = bridging.h;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		92375231240C550B00DD6076 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = VoskApiTest/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/VoskApiTest/Vosk",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.alphacephei.VoskApiTest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_OBJC_BRIDGING_HEADER = VoskApiTest/bridging.h;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		92375232240C550B00DD6076 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = VoskApiTest/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/VoskApiTest/Vosk",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.alphacephei.VoskApiTest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_OBJC_BRIDGING_HEADER = VoskApiTest/bridging.h;
				SWIFT_SWIFT3_OBJC_INFERENCE = Default;
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		92375219240C550A00DD6076 /* Build configuration list for PBXProject "VoskApiTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9237522E240C550B00DD6076 /* Debug */,
				9237522F240C550B00DD6076 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		92375230240C550B00DD6076 /* Build configuration list for PBXNativeTarget "VoskApiTest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				92375231240C550B00DD6076 /* Debug */,
				92375232240C550B00DD6076 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 92375216240C550A00DD6076 /* Project object */;
}
