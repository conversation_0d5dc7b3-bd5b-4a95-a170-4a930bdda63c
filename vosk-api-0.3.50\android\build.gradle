buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.0'
        classpath 'com.vanniktech:gradle-maven-publish-plugin:0.24.0'
    }
}

allprojects {
    version = '0.3.50'
}

subprojects {

    apply plugin: 'com.android.library'
    apply plugin: 'com.vanniktech.maven.publish'

    repositories {
        google()
        mavenCentral()
    }

    mavenPublishing {
        publishToMavenCentral(com.vanniktech.maven.publish.SonatypeHost.S01, false)
        signAllPublications()
    }

    mavenPublishing {
        pom {
            url = 'http://www.alphacephei.com.com/vosk/'
            licenses {
                license {
                    name = 'The Apache License, Version 2.0'
                    url = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                }
            }
            developers {
                developer {
                    id = 'com.alphacephei'
                    name = 'Alpha Cephei Inc'
                    email = '<EMAIL>'
                }
            }
            scm {
                connection = 'scm:git:git://github.com/alphacep/vosk-api.git'
                url = 'https://github.com/alphacep/vosk-api/'
            }
        }
    }
}
