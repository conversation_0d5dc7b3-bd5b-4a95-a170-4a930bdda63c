package main

import (
	"runtime"
	"testing"
)

func TestListMicrophones(t *testing.T) {
	// Test that the function doesn't panic and handles errors gracefully
	err := listMicrophones(false)
	if err != nil {
		t.Logf("listMicrophones returned error (this may be expected): %v", err)
	}
}

func TestListMicrophonesVerbose(t *testing.T) {
	// Test verbose mode
	err := listMicrophones(true)
	if err != nil {
		t.Logf("listMicrophones verbose returned error (this may be expected): %v", err)
	}
}

func TestPlatformDetection(t *testing.T) {
	// Test that we can detect the current platform
	switch runtime.GOOS {
	case "windows", "linux", "darwin":
		t.Logf("Detected supported platform: %s", runtime.GOOS)
	default:
		t.Logf("Detected unsupported platform: %s", runtime.GOOS)
	}
}

func TestWindowsSpecificFunction(t *testing.T) {
	if runtime.GOOS != "windows" {
		t.Skip("Skipping Windows-specific test on non-Windows platform")
	}
	
	err := listMicrophonesWindows(false)
	if err != nil {
		t.Logf("Windows microphone listing returned error (this may be expected): %v", err)
	}
}

func TestLinuxSpecificFunction(t *testing.T) {
	if runtime.GOOS != "linux" {
		t.Skip("Skipping Linux-specific test on non-Linux platform")
	}
	
	err := listMicrophonesLinux(false)
	if err != nil {
		t.Logf("Linux microphone listing returned error (this may be expected): %v", err)
	}
}

func TestMacOSSpecificFunction(t *testing.T) {
	if runtime.GOOS != "darwin" {
		t.Skip("Skipping macOS-specific test on non-macOS platform")
	}
	
	err := listMicrophonesMacOS(false)
	if err != nil {
		t.Logf("macOS microphone listing returned error (this may be expected): %v", err)
	}
}
