package main

import (
	"runtime"
	"strings"
	"testing"
	"time"
)

// TestNewSpeechRecorder tests the creation of a new speech recorder
func TestNewSpeechRecorder(t *testing.T) {
	recorder := NewSpeechRecorder()

	if recorder == nil {
		t.Fatal("NewSpeechRecorder returned nil")
	}

	if recorder.ctx == nil {
		t.<PERSON>rror("Context not initialized")
	}

	if recorder.cancel == nil {
		t.<PERSON>rror("Cancel function not initialized")
	}

	if recorder.updateInterval != 500*time.Millisecond {
		t.Errorf("Expected update interval 500ms, got %v", recorder.updateInterval)
	}

	if recorder.isRecording {
		t.Error("Recorder should not be recording initially")
	}
}

// TestDetectAudioDevices tests audio device detection
func TestDetectAudioDevices(t *testing.T) {
	recorder := NewSpeechRecorder()

	devices, err := recorder.DetectAudioDevices()
	if err != nil {
		t.Logf("Audio device detection failed (this may be expected in test environment): %v", err)
		return
	}

	if devices == nil {
		t.Error("DetectAudioDevices returned nil devices slice")
	}

	// Log detected devices for debugging
	t.Logf("Detected %d audio devices", len(devices))
	for i, device := range devices {
		t.Logf("Device %d: %s (%s)", i+1, device.Name, device.Description)
	}
}

// TestDetectWindowsDevices tests Windows-specific device detection
func TestDetectWindowsDevices(t *testing.T) {
	if runtime.GOOS != "windows" {
		t.Skip("Skipping Windows-specific test on non-Windows platform")
	}

	recorder := NewSpeechRecorder()
	devices, err := recorder.detectWindowsDevices()

	if err != nil {
		t.Logf("Windows device detection failed: %v", err)
		return
	}

	if len(devices) == 0 {
		t.Log("No Windows audio devices detected (this may be expected in test environment)")
		return
	}

	// Verify device structure
	for _, device := range devices {
		if device.Name == "" {
			t.Error("Device name should not be empty")
		}
		if device.Status == "" {
			t.Error("Device status should not be empty")
		}
	}
}

// TestDetectLinuxDevices tests Linux-specific device detection
func TestDetectLinuxDevices(t *testing.T) {
	if runtime.GOOS != "linux" {
		t.Skip("Skipping Linux-specific test on non-Linux platform")
	}

	recorder := NewSpeechRecorder()
	devices, err := recorder.detectLinuxDevices()

	if err != nil {
		t.Logf("Linux device detection failed: %v", err)
		return
	}

	t.Logf("Detected %d Linux audio devices", len(devices))
}

// TestDetectMacOSDevices tests macOS-specific device detection
func TestDetectMacOSDevices(t *testing.T) {
	if runtime.GOOS != "darwin" {
		t.Skip("Skipping macOS-specific test on non-macOS platform")
	}

	recorder := NewSpeechRecorder()
	devices, err := recorder.detectMacOSDevices()

	if err != nil {
		t.Logf("macOS device detection failed: %v", err)
		return
	}

	t.Logf("Detected %d macOS audio devices", len(devices))
}

// TestSelectDevice tests device selection logic
func TestSelectDevice(t *testing.T) {
	recorder := NewSpeechRecorder()

	// Test with empty device list
	selected := recorder.SelectDevice([]*AudioDevice{})
	if selected != nil {
		t.Error("SelectDevice should return nil for empty device list")
	}

	// Test with USB Audio Device (should be preferred)
	devices := []*AudioDevice{
		{Name: "Built-in Microphone", IsUSBAudio: false},
		{Name: "USB Audio Device", IsUSBAudio: true},
		{Name: "Bluetooth Headset", IsUSBAudio: false},
	}

	selected = recorder.SelectDevice(devices)
	if selected == nil {
		t.Fatal("SelectDevice returned nil for valid device list")
	}

	if !strings.Contains(strings.ToLower(selected.Name), "usb audio") {
		t.Errorf("Expected USB Audio Device to be selected, got %s", selected.Name)
	}

	// Test with microphone device (second priority)
	devices = []*AudioDevice{
		{Name: "Speaker", IsUSBAudio: false},
		{Name: "Built-in Microphone", IsUSBAudio: false},
		{Name: "HDMI Audio", IsUSBAudio: false},
	}

	selected = recorder.SelectDevice(devices)
	if !strings.Contains(strings.ToLower(selected.Name), "microphone") {
		t.Errorf("Expected microphone device to be selected, got %s", selected.Name)
	}

	// Test fallback to first device
	devices = []*AudioDevice{
		{Name: "Generic Audio Device", IsUSBAudio: false},
		{Name: "Another Device", IsUSBAudio: false},
	}

	selected = recorder.SelectDevice(devices)
	if selected.Name != "Generic Audio Device" {
		t.Errorf("Expected first device to be selected as fallback, got %s", selected.Name)
	}
}

// TestRecordingLifecycle tests the complete recording lifecycle
func TestRecordingLifecycle(t *testing.T) {
	recorder := NewSpeechRecorder()

	// Set up a mock device
	recorder.selectedDevice = &AudioDevice{
		Name:        "Test Device",
		Description: "Test Audio Device",
		Status:      "OK",
	}

	// Test initial state
	if recorder.isRecording {
		t.Error("Recorder should not be recording initially")
	}

	// Start recording
	err := recorder.StartRecording()
	if err != nil {
		t.Fatalf("Failed to start recording: %v", err)
	}

	// Verify recording state
	recorder.mutex.RLock()
	recording := recorder.isRecording
	recorder.mutex.RUnlock()

	if !recording {
		t.Error("Recorder should be recording after StartRecording")
	}

	// Let it run for a short time
	time.Sleep(100 * time.Millisecond)

	// Stop recording
	recorder.StopRecording()

	// Verify stopped state
	recorder.mutex.RLock()
	recording = recorder.isRecording
	recorder.mutex.RUnlock()

	if recording {
		t.Error("Recorder should not be recording after StopRecording")
	}
}

// TestTranscriptionBuffer tests the transcription buffer functionality
func TestTranscriptionBuffer(t *testing.T) {
	recorder := NewSpeechRecorder()

	// Test initial state
	recorder.mutex.RLock()
	initialText := recorder.transcriptBuffer.String()
	recorder.mutex.RUnlock()

	if initialText != "" {
		t.Error("Transcription buffer should be empty initially")
	}

	// Test writing to buffer
	testText := "Hello world"
	recorder.mutex.Lock()
	recorder.transcriptBuffer.WriteString(testText)
	recorder.mutex.Unlock()

	recorder.mutex.RLock()
	bufferText := recorder.transcriptBuffer.String()
	recorder.mutex.RUnlock()

	if bufferText != testText {
		t.Errorf("Expected buffer text '%s', got '%s'", testText, bufferText)
	}

	// Test buffer reset
	recorder.mutex.Lock()
	recorder.transcriptBuffer.Reset()
	recorder.mutex.Unlock()

	recorder.mutex.RLock()
	resetText := recorder.transcriptBuffer.String()
	recorder.mutex.RUnlock()

	if resetText != "" {
		t.Error("Transcription buffer should be empty after reset")
	}
}

// TestConcurrentAccess tests concurrent access to the recorder
func TestConcurrentAccess(t *testing.T) {
	recorder := NewSpeechRecorder()

	// Set up a mock device
	recorder.selectedDevice = &AudioDevice{
		Name:   "Test Device",
		Status: "OK",
	}

	// Start recording
	err := recorder.StartRecording()
	if err != nil {
		t.Fatalf("Failed to start recording: %v", err)
	}

	// Test concurrent access to transcription buffer
	done := make(chan bool, 2)

	// Writer goroutine
	go func() {
		for i := 0; i < 10; i++ {
			recorder.mutex.Lock()
			recorder.transcriptBuffer.WriteString("test ")
			recorder.mutex.Unlock()
			time.Sleep(10 * time.Millisecond)
		}
		done <- true
	}()

	// Reader goroutine
	go func() {
		for i := 0; i < 10; i++ {
			recorder.mutex.RLock()
			_ = recorder.transcriptBuffer.String()
			recorder.mutex.RUnlock()
			time.Sleep(10 * time.Millisecond)
		}
		done <- true
	}()

	// Wait for both goroutines to complete
	<-done
	<-done

	// Stop recording
	recorder.StopRecording()
}

// TestUpdateInterval tests the update interval timing
func TestUpdateInterval(t *testing.T) {
	recorder := NewSpeechRecorder()

	// Test default interval
	if recorder.updateInterval != 500*time.Millisecond {
		t.Errorf("Expected default update interval 500ms, got %v", recorder.updateInterval)
	}

	// Test custom interval
	customRecorder := &SpeechRecorder{
		updateInterval: 250 * time.Millisecond,
	}

	if customRecorder.updateInterval != 250*time.Millisecond {
		t.Errorf("Expected custom update interval 250ms, got %v", customRecorder.updateInterval)
	}
}

// TestContextCancellation tests proper context cancellation
func TestContextCancellation(t *testing.T) {
	recorder := NewSpeechRecorder()

	// Verify context is not cancelled initially
	select {
	case <-recorder.ctx.Done():
		t.Error("Context should not be cancelled initially")
	default:
		// Expected
	}

	// Cancel the context
	recorder.cancel()

	// Verify context is cancelled
	select {
	case <-recorder.ctx.Done():
		// Expected
	case <-time.After(100 * time.Millisecond):
		t.Error("Context should be cancelled after calling cancel()")
	}
}

// TestErrorHandling tests various error scenarios
func TestErrorHandling(t *testing.T) {
	recorder := NewSpeechRecorder()

	// Test starting recording without selected device
	err := recorder.StartRecording()
	if err == nil {
		t.Error("StartRecording should fail without selected device")
	}

	// Test with invalid device
	recorder.selectedDevice = &AudioDevice{
		Name:   "",
		Status: "ERROR",
	}

	// This should not panic even with invalid device
	recorder.StopRecording()
}

// BenchmarkDeviceDetection benchmarks device detection performance
func BenchmarkDeviceDetection(b *testing.B) {
	recorder := NewSpeechRecorder()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = recorder.DetectAudioDevices()
	}
}

// BenchmarkTranscriptionUpdate benchmarks transcription update performance
func BenchmarkTranscriptionUpdate(b *testing.B) {
	recorder := NewSpeechRecorder()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		recorder.mutex.Lock()
		recorder.transcriptBuffer.WriteString("test ")
		text := recorder.transcriptBuffer.String()
		recorder.mutex.Unlock()
		_ = text
	}
}
