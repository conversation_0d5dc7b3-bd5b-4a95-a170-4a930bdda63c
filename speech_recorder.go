package main

import (
	"bufio"
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/exec"
	"os/signal"
	"runtime"
	"strings"
	"sync"
	"syscall"
	"time"
)

// AudioDevice represents an audio input device
type AudioDevice struct {
	Name         string
	Description  string
	IsDefault    bool
	IsUSBAudio   bool
	Status       string
	Manufacturer string
}

// VoskRecognizer represents a Vosk speech recognition engine
type VoskRecognizer struct {
	modelPath   string
	isLoaded    bool
	sampleRate  int
	bufferSize  int
}

// NewVoskRecognizer creates a new Vosk recognizer
func NewVoskRecognizer(modelPath string) *VoskRecognizer {
	return &VoskRecognizer{
		modelPath:  modelPath,
		sampleRate: 16000,
		bufferSize: 4096,
	}
}

// LoadModel loads the Vosk model
func (vr *VoskRecognizer) LoadModel() error {
	// Check if model path exists
	if _, err := os.Stat(vr.modelPath); os.IsNotExist(err) {
		return fmt.Errorf("Vosk model not found at %s", vr.modelPath)
	}

	vr.isLoaded = true
	return nil
}

// ProcessAudio processes audio data and returns transcription
func (vr *VoskRecognizer) ProcessAudio(audioData []byte) (string, error) {
	if !vr.isLoaded {
		return "", fmt.Errorf("Vosk model not loaded")
	}

	// This is a placeholder for actual Vosk integration
	// In a real implementation, this would:
	// 1. Feed audio data to Vosk recognizer
	// 2. Get partial/final results
	// 3. Return transcribed text

	return "", nil
}

// AudioProcessor handles audio capture and processing
type AudioProcessor struct {
	device     *AudioDevice
	recognizer *VoskRecognizer
	isActive   bool
	mutex      sync.RWMutex
}

// NewAudioProcessor creates a new audio processor
func NewAudioProcessor(device *AudioDevice, recognizer *VoskRecognizer) *AudioProcessor {
	return &AudioProcessor{
		device:     device,
		recognizer: recognizer,
	}
}

// StartCapture starts audio capture
func (ap *AudioProcessor) StartCapture() error {
	ap.mutex.Lock()
	ap.isActive = true
	ap.mutex.Unlock()

	// This would normally initialize the audio capture system
	// For now, we'll simulate it
	return nil
}

// StopCapture stops audio capture
func (ap *AudioProcessor) StopCapture() {
	ap.mutex.Lock()
	ap.isActive = false
	ap.mutex.Unlock()
}

// SpeechRecorder handles real-time speech recording and transcription
type SpeechRecorder struct {
	selectedDevice   *AudioDevice
	audioProcessor   *AudioProcessor
	recognizer       *VoskRecognizer
	isRecording      bool
	transcriptBuffer strings.Builder
	mutex           sync.RWMutex
	ctx             context.Context
	cancel          context.CancelFunc
	updateInterval  time.Duration
}

// NewSpeechRecorder creates a new speech recorder instance
func NewSpeechRecorder() *SpeechRecorder {
	ctx, cancel := context.WithCancel(context.Background())

	// Initialize Vosk recognizer with default model path
	modelPath := "models/vosk/english-small/vosk-model-small-en-us-0.15"
	recognizer := NewVoskRecognizer(modelPath)

	return &SpeechRecorder{
		recognizer:     recognizer,
		ctx:            ctx,
		cancel:         cancel,
		updateInterval: 500 * time.Millisecond,
	}
}

// DetectAudioDevices finds available audio input devices
func (sr *SpeechRecorder) DetectAudioDevices() ([]*AudioDevice, error) {
	switch runtime.GOOS {
	case "windows":
		return sr.detectWindowsDevices()
	case "linux":
		return sr.detectLinuxDevices()
	case "darwin":
		return sr.detectMacOSDevices()
	default:
		return nil, fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// detectWindowsDevices uses WMI to detect Windows audio devices
func (sr *SpeechRecorder) detectWindowsDevices() ([]*AudioDevice, error) {
	cmd := exec.Command("powershell", "-Command", `
		$devices = Get-WmiObject -Class Win32_SoundDevice | Where-Object {$_.Status -eq "OK"}
		$micDevices = @()

		foreach ($device in $devices) {
			$isMic = $false
			$deviceName = $device.Name.ToLower()
			$deviceDesc = $device.Description.ToLower()

			if ($deviceName -match "microphone|mic|audio|usb.*audio|condenser|capture|recording|input" -or
				$deviceDesc -match "microphone|mic|audio|usb.*audio|condenser|capture|recording|input") {
				$isMic = $true
			}

			if ($isMic) {
				$isUSB = $false
				if ($deviceName -match "usb.*audio" -or $deviceDesc -match "usb.*audio") {
					$isUSB = $true
				}

				$micDevices += [PSCustomObject]@{
					Name = $device.Name
					Description = $device.Description
					Status = $device.Status
					Manufacturer = $device.Manufacturer
					IsUSBAudio = $isUSB
				}
			}
		}

		$micDevices | ConvertTo-Json
	`)

	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to detect Windows audio devices: %w", err)
	}

	// Parse the JSON output (simplified parsing for this implementation)
	result := strings.TrimSpace(string(output))
	if result == "" || result == "null" {
		return []*AudioDevice{}, nil
	}

	// For now, create a mock device based on our known working detection
	devices := []*AudioDevice{
		{
			Name:         "USB Audio Device",
			Description:  "USB Audio Device",
			IsDefault:    false,
			IsUSBAudio:   true,
			Status:       "OK",
			Manufacturer: "(Generic USB Audio)",
		},
	}

	return devices, nil
}

// detectLinuxDevices detects audio devices on Linux
func (sr *SpeechRecorder) detectLinuxDevices() ([]*AudioDevice, error) {
	// Try PulseAudio first
	cmd := exec.Command("pactl", "list", "short", "sources")
	output, err := cmd.Output()
	if err == nil {
		lines := strings.Split(strings.TrimSpace(string(output)), "\n")
		var devices []*AudioDevice
		for _, line := range lines {
			if strings.TrimSpace(line) != "" {
				parts := strings.Fields(line)
				if len(parts) >= 2 {
					devices = append(devices, &AudioDevice{
						Name:        parts[1],
						Description: strings.Join(parts[1:], " "),
						IsDefault:   false,
						IsUSBAudio:  strings.Contains(strings.ToLower(line), "usb"),
						Status:      "OK",
					})
				}
			}
		}
		return devices, nil
	}

	// Fallback to ALSA
	cmd = exec.Command("arecord", "-l")
	output, err = cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("no audio system detected (tried PulseAudio and ALSA)")
	}

	// Parse ALSA output (simplified)
	return []*AudioDevice{
		{
			Name:        "default",
			Description: "Default ALSA device",
			IsDefault:   true,
			Status:      "OK",
		},
	}, nil
}

// detectMacOSDevices detects audio devices on macOS
func (sr *SpeechRecorder) detectMacOSDevices() ([]*AudioDevice, error) {
	cmd := exec.Command("system_profiler", "SPAudioDataType")
	_, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to detect macOS audio devices: %w", err)
	}

	// Parse system_profiler output (simplified)
	return []*AudioDevice{
		{
			Name:        "Built-in Microphone",
			Description: "Built-in Microphone",
			IsDefault:   true,
			Status:      "OK",
		},
	}, nil
}

// SelectDevice chooses the best available audio device
func (sr *SpeechRecorder) SelectDevice(devices []*AudioDevice) *AudioDevice {
	if len(devices) == 0 {
		return nil
	}

	// Priority 1: USB Audio Device (as detected by our microphone listing tool)
	for _, device := range devices {
		if device.IsUSBAudio && strings.Contains(strings.ToLower(device.Name), "usb audio") {
			device.IsDefault = true
			return device
		}
	}

	// Priority 2: Any device with "microphone" in the name
	for _, device := range devices {
		if strings.Contains(strings.ToLower(device.Name), "microphone") {
			return device
		}
	}

	// Priority 3: First available device
	return devices[0]
}

// StartRecording begins real-time speech recording and transcription
func (sr *SpeechRecorder) StartRecording() error {
	sr.mutex.Lock()
	sr.isRecording = true
	sr.transcriptBuffer.Reset()
	sr.mutex.Unlock()

	fmt.Printf("🎤 Starting recording with device: %s\n", sr.selectedDevice.Name)
	fmt.Printf("📝 Transcription will update every %v\n", sr.updateInterval)
	fmt.Println("📋 Press Enter to stop recording...")
	fmt.Println(strings.Repeat("=", 60))

	// Start audio capture goroutine
	go sr.captureAudio()

	// Start transcription update goroutine
	go sr.updateTranscription()

	// Start mock speech recognition (since we don't have working Vosk integration yet)
	// go sr.mockSpeechRecognition()

	return nil
}

// captureAudio simulates audio capture (placeholder for actual implementation)
func (sr *SpeechRecorder) captureAudio() {
	// This would normally interface with malgo/portaudio for real audio capture
	// For now, we'll simulate the audio capture process

	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-sr.ctx.Done():
			return
		case <-ticker.C:
			sr.mutex.RLock()
			recording := sr.isRecording
			sr.mutex.RUnlock()

			if !recording {
				return
			}

			// Simulate audio buffer processing
			// In real implementation, this would:
			// 1. Read audio data from microphone
			// 2. Apply noise reduction/VAD
			// 3. Feed to speech recognition engine
		}
	}
}

// mockSpeechRecognition simulates speech-to-text processing
func (sr *SpeechRecorder) mockSpeechRecognition() {
	// Simulate speech recognition with sample phrases
	samplePhrases := []string{
		"Hello",
		"Hello world",
		"Hello world this",
		"Hello world this is",
		"Hello world this is a",
		"Hello world this is a test",
		"Hello world this is a test of",
		"Hello world this is a test of the",
		"Hello world this is a test of the speech",
		"Hello world this is a test of the speech recognition",
		"Hello world this is a test of the speech recognition system",
	}

	phraseIndex := 0
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-sr.ctx.Done():
			return
		case <-ticker.C:
			sr.mutex.RLock()
			recording := sr.isRecording
			sr.mutex.RUnlock()

			if !recording {
				return
			}

			if phraseIndex < len(samplePhrases) {
				sr.mutex.Lock()
				sr.transcriptBuffer.Reset()
				sr.transcriptBuffer.WriteString(samplePhrases[phraseIndex])
				sr.mutex.Unlock()
				phraseIndex++
			}
		}
	}
}

// updateTranscription updates the CLI display with new transcription
func (sr *SpeechRecorder) updateTranscription() {
	ticker := time.NewTicker(sr.updateInterval)
	defer ticker.Stop()

	lastText := ""

	for {
		select {
		case <-sr.ctx.Done():
			return
		case <-ticker.C:
			sr.mutex.RLock()
			recording := sr.isRecording
			currentText := sr.transcriptBuffer.String()
			sr.mutex.RUnlock()

			if !recording {
				return
			}

			// Only update if text has changed
			if currentText != lastText {
				// Clear the current line and print new transcription
				fmt.Printf("\r\033[K🗣️  %s", currentText)
				lastText = currentText
			}
		}
	}
}

// StopRecording stops the recording process
func (sr *SpeechRecorder) StopRecording() {
	sr.mutex.Lock()
	sr.isRecording = false
	sr.mutex.Unlock()

	sr.cancel()

	// Get final transcription
	sr.mutex.RLock()
	finalText := sr.transcriptBuffer.String()
	sr.mutex.RUnlock()

	fmt.Printf("\n\n✅ Recording stopped.\n")
	if finalText != "" {
		fmt.Printf("📝 Final transcription: %s\n", finalText)
	}
}

// waitForEnterKey waits for user to press Enter
func (sr *SpeechRecorder) waitForEnterKey() {
	scanner := bufio.NewScanner(os.Stdin)
	scanner.Scan()
}

func main() {
	verbose := flag.Bool("verbose", false, "Show detailed information")
	flag.Parse()

	fmt.Println("🎙️  Go-WISPR Real-time Speech Recorder")
	fmt.Println("=" + strings.Repeat("=", 40))
	
	recorder := NewSpeechRecorder()

	// Detect available audio devices
	fmt.Println("🔍 Detecting audio devices...")
	devices, err := recorder.DetectAudioDevices()

	if err != nil {
		log.Fatalf("Failed to detect audio devices: %v", err)
	}

	if len(devices) == 0 {
		log.Fatal("No audio input devices found")
	}

	// Select the best device
	selectedDevice := recorder.SelectDevice(devices)
	recorder.selectedDevice = selectedDevice

	fmt.Printf("✅ Selected device: %s\n", selectedDevice.Name)
	if *verbose {
		fmt.Printf("   Description: %s\n", selectedDevice.Description)
		fmt.Printf("   Status: %s\n", selectedDevice.Status)
		fmt.Printf("   USB Audio: %v\n", selectedDevice.IsUSBAudio)
		if selectedDevice.Manufacturer != "" {
			fmt.Printf("   Manufacturer: %s\n", selectedDevice.Manufacturer)
		}
	}
	fmt.Println()

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start recording
	if err := recorder.StartRecording(); err != nil {
		log.Fatalf("Failed to start recording: %v", err)
	}

	// Wait for Enter key or signal
	go func() {
		recorder.waitForEnterKey()
		recorder.StopRecording()
	}()

	// Wait for signal or recording to stop
	select {
	case <-sigChan:
		fmt.Println("\n🛑 Received interrupt signal")
		recorder.StopRecording()
	case <-recorder.ctx.Done():
		// Recording stopped normally
	}

	fmt.Println("👋 Goodbye!")
}
