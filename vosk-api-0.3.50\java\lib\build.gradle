buildscript {
  repositories {
    mavenCentral()
  }
}

plugins {
    id 'java-library'
    id 'maven-publish'
    id 'com.vanniktech.maven.publish' version '0.18.0'
}

repositories {
    mavenCentral()
}

archivesBaseName = 'vosk'
group = 'com.alphacephei'
version = '0.3.50'

mavenPublish {
    group = 'com.alphacephei'
    version = version
    sonatypeHost = 's01'
}

dependencies {
    api group: 'net.java.dev.jna', name: 'jna', version: '5.13.0'
    testImplementation 'junit:junit:4.13'
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            artifactId = 'vosk'
            from components.java
            pom {
                name = 'Vosk'
                description = 'Speech recognition library'
                url = 'http://www.alphacephei.com.com/vosk/'
                licenses {
                    license {
                        name = 'The Apache License, Version 2.0'
                        url = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                    }
                }
                developers {
                    developer {
                        id = 'alphacephei'
                        name = 'Alpha Cephei Inc'
                        email = '<EMAIL>'
                    }
                }
                scm {
                    connection = 'scm:git:git://github.com/alphacep/vosk-api.git'
                    url = 'https://github.com/alphacep/vosk-api/'
                }
            }
        }
    }
}

test {
    dependsOn cleanTest
    testLogging.showStandardStreams = true
}

java {
    withSourcesJar()
    withJavadocJar()
}
