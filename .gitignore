# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
go.work.sum

# Build artifacts
/bin/
/build/
/dist/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Audio and Model files (large files that shouldn't be in git)
*.wav
*.mp3
*.flac
*.ogg
*.m4a
*.aac
*.wma
*.bin
*.model
*.pb
*.onnx
*.tflite
*.h5
*.pkl
*.pt
*.pth

# Model directories (uncomment specific ones you want to ignore)
/models/
# /models/vosk/
# /models/whisper/
# /models/downloaded/
# /models/cache/

# Temporary files
*.tmp
*.temp
/tmp/
/temp/

# Log files
*.log
/logs/

# Configuration files with sensitive data
config.local.*
.env
.env.local
.env.*.local
secrets.json
credentials.json

# Test coverage
coverage.txt
coverage.html
coverage.out
profile.out

# Benchmark results
*.bench
benchmark_results/

# Audio recording outputs
recordings/
output/
*.recorded

# Documentation build
/docs/_build/
/docs/site/

# Backup files
*.bak
*.backup
*~

# Cache directories
.cache/
cache/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm/node files (if using any web components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python files (if using any Python scripts)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# Local development
.local/
local/