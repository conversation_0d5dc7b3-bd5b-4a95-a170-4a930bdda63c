package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	vosk "github.com/alphacep/vosk-api/go"
)

func main() {
	fmt.Println("🔍 Testing Vosk Installation...")
	fmt.Println("=" + strings.Repeat("=", 40))

	// Test 1: Check if Vosk package can be imported
	fmt.Println("✅ Vosk package imported successfully")

	// Test 2: Check if model directory exists
	modelPath := "models/vosk/english-small/vosk-model-small-en-us-0.15"
	if _, err := os.Stat(modelPath); os.IsNotExist(err) {
		fmt.Printf("❌ Vosk model not found at: %s\n", modelPath)
		fmt.Println("💡 Please download a Vosk model from: https://alphacephei.com/vosk/models")
		return
	}
	fmt.Printf("✅ Vosk model found at: %s\n", modelPath)

	// Test 3: Try to load the model
	fmt.Println("🔄 Loading Vosk model...")
	vosk.SetLogLevel(0) // Disable logs for cleaner output

	model, err := vosk.NewModel(modelPath)
	if err != nil {
		log.Fatalf("❌ Failed to load Vosk model: %v", err)
	}
	defer model.Free()
	fmt.Println("✅ Vosk model loaded successfully")

	// Test 4: Create a recognizer
	fmt.Println("🔄 Creating Vosk recognizer...")
	recognizer, err := vosk.NewRecognizer(model, 16000.0)
	if err != nil {
		log.Fatalf("❌ Failed to create Vosk recognizer: %v", err)
	}
	defer recognizer.Free()
	fmt.Println("✅ Vosk recognizer created successfully")

	// Test 5: Test with sample audio data
	fmt.Println("🔄 Testing speech recognition with sample data...")

	// Create some sample audio data (silence)
	sampleRate := 16000
	duration := 1.0 // 1 second
	numSamples := int(float64(sampleRate) * duration)
	samples := make([]int16, numSamples)

	// Fill with very quiet noise
	for i := range samples {
		samples[i] = int16((i % 100) - 50) // Very quiet noise
	}

	// Process the audio
	state := recognizer.AcceptWaveform(samples)
	var result string
	if state > 0 {
		result = recognizer.Result()
	} else {
		result = recognizer.PartialResult()
	}

	fmt.Printf("✅ Speech recognition test completed\n")
	fmt.Printf("📝 Result: %s\n", result)

	fmt.Println("\n🎉 All Vosk tests passed!")
	fmt.Println("💡 You can now use Vosk for speech recognition in your Go applications.")
}
