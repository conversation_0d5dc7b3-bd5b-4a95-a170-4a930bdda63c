FROM debian:11

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        ca-certificates \
        g++ \
        bzip2 \
        unzip \
        make \
        wget \
        git \
        python3 \
        python3-pip \
        python3-wheel \
        python3-setuptools \
        python3-cffi \
        zlib1g-dev \
        patch \
        cmake \
        automake \
        autoconf \
        libtool \
        pkg-config \
        sudo \
        g++-mingw-w64-i686 \
    && rm -rf /var/lib/apt/lists/*

RUN mkdir /opt/kaldi \
    && git clone https://github.com/alphacep/openfst \
    && cd openfst \
    && autoreconf -i \
    && CXX=i686-w64-mingw32-g++-posix CXXFLAGS="-msse2 -O3 -ftree-vectorize -DFST_NO_DYNAMIC_LINKING" \
        ./configure --prefix=/opt/kaldi/local \
        --enable-shared --enable-static --with-pic \
        --disable-bin --enable-lookahead-fsts --enable-ngram-fsts \
        --host=i686-w64-mingw32 \
    && make -j $(nproc) \
    && make install

RUN cd /opt/kaldi \
    && git clone -b v0.3.20 --single-branch https://github.com/xianyi/OpenBLAS \
    && cd OpenBLAS \
    && make HOSTCC=gcc BINARY=32 CC=i686-w64-mingw32-gcc ONLY_CBLAS=1 DYNAMIC_ARCH=1 TARGET=NEHALEM USE_LOCKING=1 USE_THREAD=0 -j $(nproc) \
    && make PREFIX=/opt/kaldi/local install

RUN cd /opt/kaldi \
    && git clone -b v3.2.1  --single-branch https://github.com/alphacep/clapack \
    && mkdir clapack/BUILD \
    && cd clapack/BUILD \
    && cmake -DCMAKE_C_COMPILER_TARGET=i686-w64-mingw32 -DCMAKE_C_COMPILER=i686-w64-mingw32-gcc-posix -DCMAKE_SYSTEM_NAME=Windows -DCMAKE_CROSSCOMPILING=True .. \
    && make -C F2CLIBS/libf2c \
    && make -C BLAS \
    && make -C SRC \
    && find . -name *.a -exec cp {} /opt/kaldi/local/lib \;

RUN cd /opt/kaldi \
    && git clone -b vosk-android --single-branch https://github.com/alphacep/kaldi \
    && cd kaldi/src \
    && CXX=i686-w64-mingw32-g++-posix CXXFLAGS="-O3 -ftree-vectorize -DFST_NO_DYNAMIC_LINKING" ./configure --shared --mingw=yes --use-cuda=no \
        --mathlib=OPENBLAS_CLAPACK \
        --host=i686-w64-mingw32 --openblas-clapack-root=/opt/kaldi/local \
        --fst-root=/opt/kaldi/local --fst-version=1.8.0 \
    && make depend -j \
    && make -j $(nproc) online2 rnnlm
