# Fix Vosk Directory Structure
# This script reorganizes the Vosk files to the expected structure

Write-Host "Fixing Vosk directory structure..." -ForegroundColor Green

$voskRoot = "C:\vosk"
$voskApiDir = "$voskRoot\vosk-api-0.3.50"

# Check if the vosk-api-0.3.50 directory exists
if (Test-Path $voskApiDir) {
    Write-Host "Found vosk-api-0.3.50 directory" -ForegroundColor Cyan
    
    # List contents to see what we have
    Write-Host "Contents of vosk-api-0.3.50:" -ForegroundColor Yellow
    Get-ChildItem $voskApiDir | ForEach-Object { Write-Host "  $($_.Name)" -ForegroundColor White }
    
    # Look for lib and include directories
    $libDir = "$voskApiDir\lib"
    $includeDir = "$voskApiDir\include"
    $binDir = "$voskApiDir\bin"
    
    if (Test-Path $libDir) {
        Write-Host "Found lib directory" -ForegroundColor Green
        Write-Host "Lib contents:" -ForegroundColor Yellow
        Get-ChildItem $libDir | ForEach-Object { Write-Host "  $($_.Name)" -ForegroundColor White }
        
        # Move lib directory to root
        if (!(Test-Path "$voskRoot\lib")) {
            Move-Item $libDir "$voskRoot\lib"
            Write-Host "Moved lib directory to C:\vosk\lib" -ForegroundColor Green
        } else {
            Write-Host "C:\vosk\lib already exists, copying files..." -ForegroundColor Yellow
            Copy-Item "$libDir\*" "$voskRoot\lib" -Recurse -Force
        }
    }
    
    if (Test-Path $includeDir) {
        Write-Host "Found include directory" -ForegroundColor Green
        Write-Host "Include contents:" -ForegroundColor Yellow
        Get-ChildItem $includeDir | ForEach-Object { Write-Host "  $($_.Name)" -ForegroundColor White }
        
        # Move include directory to root
        if (!(Test-Path "$voskRoot\include")) {
            Move-Item $includeDir "$voskRoot\include"
            Write-Host "Moved include directory to C:\vosk\include" -ForegroundColor Green
        } else {
            Write-Host "C:\vosk\include already exists, copying files..." -ForegroundColor Yellow
            Copy-Item "$includeDir\*" "$voskRoot\include" -Recurse -Force
        }
    }
    
    if (Test-Path $binDir) {
        Write-Host "Found bin directory" -ForegroundColor Green
        Write-Host "Bin contents:" -ForegroundColor Yellow
        Get-ChildItem $binDir | ForEach-Object { Write-Host "  $($_.Name)" -ForegroundColor White }
        
        # Move bin directory to root
        if (!(Test-Path "$voskRoot\bin")) {
            Move-Item $binDir "$voskRoot\bin"
            Write-Host "Moved bin directory to C:\vosk\bin" -ForegroundColor Green
        } else {
            Write-Host "C:\vosk\bin already exists, copying files..." -ForegroundColor Yellow
            Copy-Item "$binDir\*" "$voskRoot\bin" -Recurse -Force
        }
    }
    
    # Clean up the vosk-api-0.3.50 directory if it's empty
    $remainingItems = Get-ChildItem $voskApiDir
    if ($remainingItems.Count -eq 0) {
        Remove-Item $voskApiDir
        Write-Host "Removed empty vosk-api-0.3.50 directory" -ForegroundColor Green
    } else {
        Write-Host "vosk-api-0.3.50 directory still contains:" -ForegroundColor Yellow
        $remainingItems | ForEach-Object { Write-Host "  $($_.Name)" -ForegroundColor White }
    }
    
} else {
    Write-Host "vosk-api-0.3.50 directory not found" -ForegroundColor Red
    Write-Host "Current contents of C:\vosk:" -ForegroundColor Yellow
    if (Test-Path $voskRoot) {
        Get-ChildItem $voskRoot | ForEach-Object { Write-Host "  $($_.Name)" -ForegroundColor White }
    } else {
        Write-Host "C:\vosk directory does not exist" -ForegroundColor Red
    }
}

# Verify the final structure
Write-Host ""
Write-Host "Final Vosk directory structure:" -ForegroundColor Green
if (Test-Path "$voskRoot\lib") {
    Write-Host "SUCCESS: C:\vosk\lib exists" -ForegroundColor Green
    $libFiles = Get-ChildItem "$voskRoot\lib" -File
    Write-Host "  Library files: $($libFiles.Count)" -ForegroundColor Cyan
    $libFiles | ForEach-Object { Write-Host "    $($_.Name)" -ForegroundColor White }
} else {
    Write-Host "ERROR: C:\vosk\lib not found" -ForegroundColor Red
}

if (Test-Path "$voskRoot\include") {
    Write-Host "SUCCESS: C:\vosk\include exists" -ForegroundColor Green
    $headerFiles = Get-ChildItem "$voskRoot\include" -File
    Write-Host "  Header files: $($headerFiles.Count)" -ForegroundColor Cyan
    $headerFiles | ForEach-Object { Write-Host "    $($_.Name)" -ForegroundColor White }
} else {
    Write-Host "ERROR: C:\vosk\include not found" -ForegroundColor Red
}

if (Test-Path "$voskRoot\bin") {
    Write-Host "SUCCESS: C:\vosk\bin exists" -ForegroundColor Green
    $binFiles = Get-ChildItem "$voskRoot\bin" -File
    Write-Host "  Binary files: $($binFiles.Count)" -ForegroundColor Cyan
    $binFiles | ForEach-Object { Write-Host "    $($_.Name)" -ForegroundColor White }
}

Write-Host ""
Write-Host "Directory structure fix completed!" -ForegroundColor Green
