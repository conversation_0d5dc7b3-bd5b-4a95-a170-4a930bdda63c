{"name": "vosk", "version": "0.3.50", "description": "Node binding for continuous offline voice recoginition with Vosk library.", "repository": {"type": "git", "url": "git://github.com/alphacep/vosk-api.git"}, "main": "index.js", "keywords": ["speech", "speech recognition", "voice"], "author": "Alpha Cephei Inc.", "license": "Apache-2.0", "engines": {"node": ">= 12.x.x"}, "dependencies": {"async": "^3.2.0", "ffi-napi": "^4.0.3", "mic": "^2.1.2", "ref-napi": ">=2.0.0", "wav": "^1.0.2"}}