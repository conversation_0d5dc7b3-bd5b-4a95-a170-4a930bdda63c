{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "Vosk Training", "provenance": [], "collapsed_sections": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "gpuClass": "standard"}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Vdqvrs5TYr2Y", "outputId": "206d38b4-0819-488a-eb05-53a80d766bc5"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["/content\n", "--2022-08-17 10:53:18--  https://alphacephei.com/vosk-colab/kaldi.tar.gz\n", "Resolving alphacephei.com (alphacephei.com)... ************, 2a01:4f8:13a:279f::2\n", "Connecting to alphacephei.com (alphacephei.com)|************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 809174554 (772M) [application/octet-stream]\n", "Saving to: ‘kaldi.tar.gz’\n", "\n", "kaldi.tar.gz        100%[===================>] 771.69M   108MB/s    in 7.2s    \n", "\n", "2022-08-17 10:53:25 (106 MB/s) - ‘kaldi.tar.gz’ saved [809174554/809174554]\n", "\n"]}], "source": ["%cd /content\n", "!wget -c https://alphacephei.com/vosk-colab/kaldi.tar.gz\n", "!tar xzf kaldi.tar.gz"]}, {"cell_type": "code", "source": ["%cd /content\n", "!rm -rf vosk-api\n", "!git clone https://github.com/alphacep/vosk-api"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OfUtX42VZ1zl", "outputId": "0cbed826-bac2-4e15-dfa2-2a101b3562dd"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["/content\n", "Cloning into 'vosk-api'...\n", "remote: Enumerating objects: 3259, done.\u001b[K\n", "remote: Counting objects: 100% (124/124), done.\u001b[K\n", "remote: Compressing objects: 100% (92/92), done.\u001b[K\n", "remote: Total 3259 (delta 45), reused 92 (delta 28), pack-reused 3135\u001b[K\n", "Receiving objects: 100% (3259/3259), 1.07 MiB | 8.46 MiB/s, done.\n", "Resolving deltas: 100% (1847/1847), done.\n"]}]}, {"cell_type": "code", "source": ["!rm -rf /content/kaldi/egs/ac/training\n", "!cp -r /content/vosk-api/training /content/kaldi/egs/ac"], "metadata": {"id": "bwdTkMVpZ4Pb"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["!apt install flac"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xvC_AENtasCF", "outputId": "d0740818-836c-40d4-9e44-3f966e83128c"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Reading package lists... Done\n", "Building dependency tree       \n", "Reading state information... Done\n", "The following package was automatically installed and is no longer required:\n", "  libnvidia-common-460\n", "Use 'apt autoremove' to remove it.\n", "The following NEW packages will be installed:\n", "  flac\n", "0 upgraded, 1 newly installed, 0 to remove and 20 not upgraded.\n", "Need to get 144 kB of archives.\n", "After this operation, 438 kB of additional disk space will be used.\n", "Get:1 http://archive.ubuntu.com/ubuntu bionic/universe amd64 flac amd64 1.3.2-1 [144 kB]\n", "Fetched 144 kB in 0s (294 kB/s)\n", "Selecting previously unselected package flac.\n", "(Reading database ... 155676 files and directories currently installed.)\n", "Preparing to unpack .../flac_1.3.2-1_amd64.deb ...\n", "Unpacking flac (1.3.2-1) ...\n", "Setting up flac (1.3.2-1) ...\n", "Processing triggers for man-db (2.8.3-2ubuntu0.1) ...\n"]}]}, {"cell_type": "code", "source": ["%cd /content/kaldi/egs/ac/training\n", "!rm -rf exp\n", "!sed -i 's:--nj 10:--nj 2:g' run.sh\n", "!cat run.sh\n", "!bash run.sh --stop-stage 3"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Lv1cbjZuaE6O", "outputId": "13cb7312-97d6-42ec-910a-8095ba1eb967"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["/content/kaldi/egs/ac/training\n", "#!/usr/bin/env bash\n", "\n", ". ./cmd.sh\n", ". ./path.sh\n", "\n", "stage=-1\n", "stop_stage=100\n", "\n", ". utils/parse_options.sh\n", "\n", "# Data preparation\n", "if [ ${stage} -le 0 ] && [ ${stop_stage} -ge 0 ]; then\n", "  data_url=www.openslr.org/resources/31\n", "  lm_url=www.openslr.org/resources/11\n", "  database=corpus\n", "\n", "  mkdir -p $database\n", "  for part in dev-clean-2 train-clean-5; do\n", "    local/download_and_untar.sh $database $data_url $part\n", "  done\n", "\n", "  local/download_lm.sh $lm_url $database data/local/lm\n", "\n", "  local/data_prep.sh $database/LibriSpeech/train-clean-5 data/train\n", "  local/data_prep.sh $database/LibriSpeech/dev-clean-2 data/test\n", "fi\n", "\n", "# Dictionary formatting\n", "if [ ${stage} -le 1 ] && [ ${stop_stage} -ge 1 ]; then\n", "  local/prepare_dict.sh data/local/lm data/local/dict\n", "  utils/prepare_lang.sh data/local/dict \"<UNK>\" data/local/lang data/lang\n", "fi\n", "\n", "# Extract MFCC features\n", "if [ ${stage} -le 2 ] && [ ${stop_stage} -ge 2 ]; then\n", "  for task in train; do\n", "    steps/make_mfcc.sh --cmd \"$train_cmd\" --nj 2 data/$task exp/make_mfcc/$task $mfcc\n", "    steps/compute_cmvn_stats.sh data/$task exp/make_mfcc/$task $mfcc\n", "  done\n", "fi\n", "\n", "# Train GMM models\n", "if [ ${stage} -le 3 ] && [ ${stop_stage} -ge 3 ]; then\n", "  steps/train_mono.sh --nj 2 --cmd \"$train_cmd\" \\\n", "    data/train data/lang exp/mono\n", "\n", "  steps/align_si.sh  --nj 2 --cmd \"$train_cmd\" \\\n", "    data/train data/lang exp/mono exp/mono_ali\n", "\n", "  steps/train_lda_mllt.sh  --cmd \"$train_cmd\" \\\n", "    2000 10000 data/train data/lang exp/mono_ali exp/tri1\n", "\n", "  steps/align_si.sh --nj 2 --cmd \"$train_cmd\" \\\n", "    data/train data/lang exp/tri1 exp/tri1_ali\n", "\n", "  steps/train_lda_mllt.sh --cmd \"$train_cmd\" \\\n", "    2500 15000 data/train data/lang exp/tri1_ali exp/tri2\n", "\n", "  steps/align_si.sh  --nj 2 --cmd \"$train_cmd\" \\\n", "    data/train data/lang exp/tri2 exp/tri2_ali\n", "\n", "  steps/train_lda_mllt.sh --cmd \"$train_cmd\" \\\n", "    2500 20000 data/train data/lang exp/tri2_ali exp/tri3\n", "\n", "  steps/align_si.sh  --nj 2 --cmd \"$train_cmd\" \\\n", "    data/train data/lang exp/tri3 exp/tri3_ali\n", "fi\n", "\n", "# Train TDNN model\n", "if [ ${stage} -le 4 ] && [ ${stop_stage} -ge 4 ]; then\n", "  local/chain/run_tdnn.sh\n", "fi\n", "\n", "# Decode\n", "if [ ${stage} -le 5 ] && [ ${stop_stage} -ge 5 ]; then\n", "\n", "  utils/format_lm.sh data/lang data/local/lm/lm_tgsmall.arpa.gz data/local/dict/lexicon.txt data/lang_test\n", "  utils/mkgraph.sh --self-loop-scale 1.0 data/lang_test exp/chain/tdnn exp/chain/tdnn/graph\n", "  utils/build_const_arpa_lm.sh data/local/lm/lm_tgmed.arpa.gz \\\n", "    data/lang data/lang_test_rescore\n", "\n", "  for task in test; do\n", "\n", "    steps/make_mfcc.sh --cmd \"$train_cmd\" --nj 2 data/$task exp/make_mfcc/$task $mfcc\n", "    steps/compute_cmvn_stats.sh data/$task exp/make_mfcc/$task $mfcc\n", "\n", "    steps/online/nnet2/extract_ivectors_online.sh --nj 2 \\\n", "        data/${task} exp/chain/extractor \\\n", "        exp/chain/ivectors_${task}\n", "\n", "    steps/nnet3/decode.sh --cmd $decode_cmd --num-threads 10 --nj 1 \\\n", "         --beam 13.0 --max-active 7000 --lattice-beam 4.0 \\\n", "         --online-ivector-dir exp/chain/ivectors_${task} \\\n", "         --acwt 1.0 --post-decode-acwt 10.0 \\\n", "         exp/chain/tdnn/graph data/${task} exp/chain/tdnn/decode_${task}\n", "\n", "    steps/lmrescore_const_arpa.sh data/lang_test data/lang_test_rescore \\\n", "        data/${task} exp/chain/tdnn/decode_${task} exp/chain/tdnn/decode_${task}_rescore\n", "  done\n", "\n", "  bash RESULTS\n", "fi\n", "local/download_and_untar.sh: downloading data from www.openslr.org/resources/31/dev-clean-2.tar.gz.  This may take some time, please be patient.\n", "--2022-08-17 12:36:38--  http://www.openslr.org/resources/31/dev-clean-2.tar.gz\n", "Resolving www.openslr.org (www.openslr.org)... *************\n", "Connecting to www.openslr.org (www.openslr.org)|*************|:80... connected.\n", "HTTP request sent, awaiting response... 302 Found\n", "Location: https://us.openslr.org/resources/31/dev-clean-2.tar.gz [following]\n", "--2022-08-17 12:36:38--  https://us.openslr.org/resources/31/dev-clean-2.tar.gz\n", "Resolving us.openslr.org (us.openslr.org)... *************\n", "Connecting to us.openslr.org (us.openslr.org)|*************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 126046265 (120M) [application/x-gzip]\n", "Saving to: 'dev-clean-2.tar.gz'\n", "\n", "dev-clean-2.tar.gz  100%[===================>] 120.21M   106MB/s    in 1.1s    \n", "\n", "2022-08-17 12:36:39 (106 MB/s) - 'dev-clean-2.tar.gz' saved [126046265/126046265]\n", "\n", "/content/kaldi/egs/ac/training\n", "LibriSpeech/dev-clean-2/\n", "LibriSpeech/dev-clean-2/5694/\n", "LibriSpeech/dev-clean-2/5694/64038/\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038.trans.txt\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0000.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0001.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0002.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0003.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0004.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0005.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0006.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0007.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0008.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0009.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0010.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0011.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0012.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0013.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0014.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0015.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0016.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0017.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0018.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0019.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0020.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0021.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0022.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0023.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0024.flac\n", "LibriSpeech/dev-clean-2/5694/64038/5694-64038-0025.flac\n", "LibriSpeech/dev-clean-2/3000/\n", "LibriSpeech/dev-clean-2/3000/15664/\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664.trans.txt\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0000.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0001.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0002.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0003.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0004.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0005.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0006.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0007.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0008.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0009.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0010.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0011.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0012.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0013.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0014.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0015.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0016.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0017.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0018.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0019.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0020.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0021.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0022.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0023.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0024.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0025.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0026.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0027.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0028.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0029.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0030.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0031.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0032.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0033.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0034.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0035.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0036.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0037.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0038.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0039.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0040.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0041.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0042.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0043.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0044.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0045.flac\n", "LibriSpeech/dev-clean-2/3000/15664/3000-15664-0046.flac\n", "LibriSpeech/dev-clean-2/7850/\n", "LibriSpeech/dev-clean-2/7850/286674/\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674.trans.txt\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0000.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0001.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0002.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0003.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0004.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0005.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0006.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0007.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0008.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0009.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0010.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0011.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0012.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0013.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0014.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0015.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0016.flac\n", "LibriSpeech/dev-clean-2/7850/286674/7850-286674-0017.flac\n", "LibriSpeech/dev-clean-2/7850/281318/\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318.trans.txt\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0000.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0001.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0002.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0003.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0004.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0005.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0006.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0007.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0008.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0009.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0010.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0011.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0012.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0013.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0014.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0015.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0016.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0017.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0018.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0019.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0020.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0021.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0022.flac\n", "LibriSpeech/dev-clean-2/7850/281318/7850-281318-0023.flac\n", "LibriSpeech/dev-clean-2/84/\n", "LibriSpeech/dev-clean-2/84/121550/\n", "LibriSpeech/dev-clean-2/84/121550/84-121550.trans.txt\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0000.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0001.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0002.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0003.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0004.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0005.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0006.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0007.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0008.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0009.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0010.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0011.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0012.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0013.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0014.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0015.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0016.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0017.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0018.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0019.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0020.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0021.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0022.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0023.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0024.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0025.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0026.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0027.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0028.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0029.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0030.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0031.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0032.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0033.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0034.flac\n", "LibriSpeech/dev-clean-2/84/121550/84-121550-0035.flac\n", "LibriSpeech/dev-clean-2/7976/\n", "LibriSpeech/dev-clean-2/7976/110523/\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523.trans.txt\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0000.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0001.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0002.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0003.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0004.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0005.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0006.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0007.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0008.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0009.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0010.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0011.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0012.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0013.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0014.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0015.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0016.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0017.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0018.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0019.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0020.flac\n", "LibriSpeech/dev-clean-2/7976/110523/7976-110523-0021.flac\n", "LibriSpeech/dev-clean-2/5895/\n", "LibriSpeech/dev-clean-2/5895/34622/\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622.trans.txt\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0000.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0001.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0002.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0003.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0004.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0005.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0006.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0007.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0008.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0009.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0010.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0011.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0012.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0013.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0014.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0015.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0016.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0017.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0018.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0019.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0020.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0021.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0022.flac\n", "LibriSpeech/dev-clean-2/5895/34622/5895-34622-0023.flac\n", "LibriSpeech/dev-clean-2/5895/34615/\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615.trans.txt\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0000.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0001.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0002.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0003.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0004.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0005.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0006.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0007.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0008.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0009.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0010.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0011.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0012.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0013.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0014.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0015.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0016.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0017.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0018.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0019.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0020.flac\n", "LibriSpeech/dev-clean-2/5895/34615/5895-34615-0021.flac\n", "LibriSpeech/dev-clean-2/5895/34629/\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629.trans.txt\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0000.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0001.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0002.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0003.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0004.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0005.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0006.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0007.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0008.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0009.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0010.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0011.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0012.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0013.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0014.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0015.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0016.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0017.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0018.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0019.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0020.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0021.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0022.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0023.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0024.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0025.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0026.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0027.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0028.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0029.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0030.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0031.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0032.flac\n", "LibriSpeech/dev-clean-2/5895/34629/5895-34629-0033.flac\n", "LibriSpeech/dev-clean-2/6241/\n", "LibriSpeech/dev-clean-2/6241/61946/\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946.trans.txt\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0000.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0001.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0002.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0003.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0004.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0005.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0006.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0007.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0008.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0009.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0010.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0011.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0012.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0013.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0014.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0015.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0016.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0017.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0018.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0019.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0020.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0021.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0022.flac\n", "LibriSpeech/dev-clean-2/6241/61946/6241-61946-0023.flac\n", "LibriSpeech/dev-clean-2/6241/61943/\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943.trans.txt\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0000.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0001.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0002.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0003.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0004.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0005.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0006.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0007.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0008.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0009.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0010.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0011.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0012.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0013.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0014.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0015.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0016.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0017.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0018.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0019.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0020.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0021.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0022.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0023.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0024.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0025.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0026.flac\n", "LibriSpeech/dev-clean-2/6241/61943/6241-61943-0027.flac\n", "LibriSpeech/dev-clean-2/1462/\n", "LibriSpeech/dev-clean-2/1462/170142/\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142.trans.txt\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0000.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0001.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0002.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0003.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0004.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0005.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0006.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0007.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0008.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0009.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0010.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0011.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0012.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0013.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0014.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0015.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0016.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0017.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0018.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0019.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0020.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0021.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0022.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0023.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0024.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0025.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0026.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0027.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0028.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0029.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0030.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0031.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0032.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0033.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0034.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0035.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0036.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0037.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0038.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0039.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0040.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0041.flac\n", "LibriSpeech/dev-clean-2/1462/170142/1462-170142-0042.flac\n", "LibriSpeech/dev-clean-2/1462/170145/\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145.trans.txt\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0000.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0001.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0002.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0003.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0004.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0005.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0006.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0007.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0008.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0009.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0010.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0011.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0012.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0013.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0014.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0015.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0016.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0017.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0018.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0019.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0020.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0021.flac\n", "LibriSpeech/dev-clean-2/1462/170145/1462-170145-0022.flac\n", "LibriSpeech/dev-clean-2/3536/\n", "LibriSpeech/dev-clean-2/3536/23268/\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268.trans.txt\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0000.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0001.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0002.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0003.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0004.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0005.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0006.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0007.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0008.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0009.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0010.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0011.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0012.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0013.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0014.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0015.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0016.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0017.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0018.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0019.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0020.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0021.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0022.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0023.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0024.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0025.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0026.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0027.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0028.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0029.flac\n", "LibriSpeech/dev-clean-2/3536/23268/3536-23268-0030.flac\n", "LibriSpeech/dev-clean-2/6319/\n", "LibriSpeech/dev-clean-2/6319/57405/\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405.trans.txt\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0000.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0001.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0002.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0003.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0004.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0005.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0006.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0007.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0008.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0009.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0010.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0011.flac\n", "LibriSpeech/dev-clean-2/6319/57405/6319-57405-0012.flac\n", "LibriSpeech/dev-clean-2/2035/\n", "LibriSpeech/dev-clean-2/2035/147961/\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961.trans.txt\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0000.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0001.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0002.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0003.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0004.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0005.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0006.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0007.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0008.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0009.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0010.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0011.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0012.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0013.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0014.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0015.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0016.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0017.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0018.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0019.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0020.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0021.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0022.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0023.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0024.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0025.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0026.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0027.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0028.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0029.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0030.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0031.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0032.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0033.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0034.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0035.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0036.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0037.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0038.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0039.flac\n", "LibriSpeech/dev-clean-2/2035/147961/2035-147961-0040.flac\n", "LibriSpeech/dev-clean-2/2035/152373/\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373.trans.txt\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0000.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0001.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0002.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0003.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0004.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0005.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0006.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0007.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0008.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0009.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0010.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0011.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0012.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0013.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0014.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0015.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0016.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0017.flac\n", "LibriSpeech/dev-clean-2/2035/152373/2035-152373-0018.flac\n", "LibriSpeech/dev-clean-2/2035/147960/\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960.trans.txt\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0000.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0001.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0002.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0003.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0004.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0005.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0006.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0007.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0008.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0009.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0010.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0011.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0012.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0013.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0014.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0015.flac\n", "LibriSpeech/dev-clean-2/2035/147960/2035-147960-0016.flac\n", "LibriSpeech/dev-clean-2/5338/\n", "LibriSpeech/dev-clean-2/5338/24640/\n", "LibriSpeech/dev-clean-2/5338/24640/5338-24640.trans.txt\n", "LibriSpeech/dev-clean-2/5338/24640/5338-24640-0000.flac\n", "LibriSpeech/dev-clean-2/5338/24640/5338-24640-0001.flac\n", "LibriSpeech/dev-clean-2/5338/24640/5338-24640-0002.flac\n", "LibriSpeech/dev-clean-2/5338/24640/5338-24640-0003.flac\n", "LibriSpeech/dev-clean-2/5338/24640/5338-24640-0004.flac\n", "LibriSpeech/dev-clean-2/5338/24640/5338-24640-0005.flac\n", "LibriSpeech/dev-clean-2/5338/24640/5338-24640-0006.flac\n", "LibriSpeech/dev-clean-2/5338/24640/5338-24640-0007.flac\n", "LibriSpeech/dev-clean-2/5338/24640/5338-24640-0008.flac\n", "LibriSpeech/dev-clean-2/5338/24640/5338-24640-0009.flac\n", "LibriSpeech/dev-clean-2/5338/284437/\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437.trans.txt\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0000.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0001.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0002.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0003.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0004.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0005.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0006.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0007.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0008.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0009.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0010.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0011.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0012.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0013.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0014.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0015.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0016.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0017.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0018.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0019.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0020.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0021.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0022.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0023.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0024.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0025.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0026.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0027.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0028.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0029.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0030.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0031.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0032.flac\n", "LibriSpeech/dev-clean-2/5338/284437/5338-284437-0033.flac\n", "LibriSpeech/dev-clean-2/1988/\n", "LibriSpeech/dev-clean-2/1988/24833/\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833.trans.txt\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0000.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0001.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0002.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0003.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0004.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0005.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0006.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0007.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0008.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0009.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0010.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0011.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0012.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0013.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0014.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0015.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0016.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0017.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0018.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0019.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0020.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0021.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0022.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0023.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0024.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0025.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0026.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0027.flac\n", "LibriSpeech/dev-clean-2/1988/24833/1988-24833-0028.flac\n", "LibriSpeech/dev-clean-2/1988/147956/\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956.trans.txt\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0000.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0001.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0002.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0003.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0004.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0005.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0006.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0007.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0008.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0009.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0010.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0011.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0012.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0013.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0014.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0015.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0016.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0017.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0018.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0019.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0020.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0021.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0022.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0023.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0024.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0025.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0026.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0027.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0028.flac\n", "LibriSpeech/dev-clean-2/1988/147956/1988-147956-0029.flac\n", "LibriSpeech/dev-clean-2/8842/\n", "LibriSpeech/dev-clean-2/8842/304647/\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647.trans.txt\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0000.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0001.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0002.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0003.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0004.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0005.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0006.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0007.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0008.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0009.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0010.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0011.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0012.flac\n", "LibriSpeech/dev-clean-2/8842/304647/8842-304647-0013.flac\n", "LibriSpeech/dev-clean-2/1272/\n", "LibriSpeech/dev-clean-2/1272/141231/\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231.trans.txt\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0000.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0001.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0002.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0003.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0004.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0005.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0006.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0007.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0008.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0009.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0010.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0011.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0012.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0013.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0014.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0015.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0016.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0017.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0018.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0019.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0020.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0021.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0022.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0023.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0024.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0025.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0026.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0027.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0028.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0029.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0030.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0031.flac\n", "LibriSpeech/dev-clean-2/1272/141231/1272-141231-0032.flac\n", "LibriSpeech/dev-clean-2/1272/135031/\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031.trans.txt\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0000.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0001.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0002.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0003.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0004.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0005.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0006.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0007.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0008.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0009.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0010.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0011.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0012.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0013.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0014.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0015.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0016.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0017.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0018.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0019.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0020.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0021.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0022.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0023.flac\n", "LibriSpeech/dev-clean-2/1272/135031/1272-135031-0024.flac\n", "LibriSpeech/dev-clean-2/1993/\n", "LibriSpeech/dev-clean-2/1993/147964/\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964.trans.txt\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964-0000.flac\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964-0001.flac\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964-0002.flac\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964-0003.flac\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964-0004.flac\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964-0005.flac\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964-0006.flac\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964-0007.flac\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964-0008.flac\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964-0009.flac\n", "LibriSpeech/dev-clean-2/1993/147964/1993-147964-0010.flac\n", "LibriSpeech/dev-clean-2/2428/\n", "LibriSpeech/dev-clean-2/2428/83699/\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699.trans.txt\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0000.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0001.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0002.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0003.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0004.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0005.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0006.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0007.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0008.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0009.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0010.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0011.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0012.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0013.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0014.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0015.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0016.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0017.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0018.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0019.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0020.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0021.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0022.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0023.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0024.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0025.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0026.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0027.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0028.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0029.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0030.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0031.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0032.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0033.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0034.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0035.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0036.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0037.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0038.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0039.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0040.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0041.flac\n", "LibriSpeech/dev-clean-2/2428/83699/2428-83699-0042.flac\n", "LibriSpeech/dev-clean-2/2803/\n", "LibriSpeech/dev-clean-2/2803/161169/\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169.trans.txt\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0000.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0001.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0002.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0003.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0004.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0005.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0006.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0007.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0008.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0009.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0010.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0011.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0012.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0013.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0014.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0015.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0016.flac\n", "LibriSpeech/dev-clean-2/2803/161169/2803-161169-0017.flac\n", "LibriSpeech/dev-clean-2/2803/154320/\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320.trans.txt\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0000.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0001.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0002.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0003.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0004.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0005.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0006.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0007.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0008.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0009.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0010.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0011.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0012.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0013.flac\n", "LibriSpeech/dev-clean-2/2803/154320/2803-154320-0014.flac\n", "LibriSpeech/dev-clean-2/8297/\n", "LibriSpeech/dev-clean-2/8297/275156/\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156.trans.txt\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0000.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0001.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0002.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0003.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0004.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0005.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0006.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0007.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0008.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0009.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0010.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0011.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0012.flac\n", "LibriSpeech/dev-clean-2/8297/275156/8297-275156-0013.flac\n", "LibriSpeech/dev-clean-2/6295/\n", "LibriSpeech/dev-clean-2/6295/244435/\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435.trans.txt\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0000.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0001.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0002.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0003.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0004.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0005.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0006.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0007.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0008.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0009.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0010.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0011.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0012.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0013.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0014.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0015.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0016.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0017.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0018.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0019.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0020.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0021.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0022.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0023.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0024.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0025.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0026.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0027.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0028.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0029.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0030.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0031.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0032.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0033.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0034.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0035.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0036.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0037.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0038.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0039.flac\n", "LibriSpeech/dev-clean-2/6295/244435/6295-244435-0040.flac\n", "LibriSpeech/dev-clean-2/3576/\n", "LibriSpeech/dev-clean-2/3576/138058/\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058.trans.txt\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0000.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0001.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0002.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0003.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0004.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0005.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0006.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0007.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0008.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0009.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0010.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0011.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0012.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0013.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0014.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0015.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0016.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0017.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0018.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0019.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0020.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0021.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0022.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0023.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0024.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0025.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0026.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0027.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0028.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0029.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0030.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0031.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0032.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0033.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0034.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0035.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0036.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0037.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0038.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0039.flac\n", "LibriSpeech/dev-clean-2/3576/138058/3576-138058-0040.flac\n", "LibriSpeech/dev-clean-2/2412/\n", "LibriSpeech/dev-clean-2/2412/153948/\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948.trans.txt\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0000.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0001.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0002.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0003.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0004.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0005.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0006.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0007.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0008.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0009.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0010.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0011.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0012.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0013.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0014.flac\n", "LibriSpeech/dev-clean-2/2412/153948/2412-153948-0015.flac\n", "LibriSpeech/dev-clean-2/251/\n", "LibriSpeech/dev-clean-2/251/118436/\n", "LibriSpeech/dev-clean-2/251/118436/251-118436.trans.txt\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0000.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0001.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0002.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0003.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0004.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0005.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0006.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0007.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0008.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0009.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0010.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0011.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0012.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0013.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0014.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0015.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0016.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0017.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0018.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0019.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0020.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0021.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0022.flac\n", "LibriSpeech/dev-clean-2/251/118436/251-118436-0023.flac\n", "LibriSpeech/dev-clean-2/251/136532/\n", "LibriSpeech/dev-clean-2/251/136532/251-136532.trans.txt\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0000.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0001.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0002.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0003.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0004.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0005.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0006.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0007.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0008.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0009.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0010.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0011.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0012.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0013.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0014.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0015.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0016.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0017.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0018.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0019.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0020.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0021.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0022.flac\n", "LibriSpeech/dev-clean-2/251/136532/251-136532-0023.flac\n", "LibriSpeech/dev-clean-2/777/\n", "LibriSpeech/dev-clean-2/777/126732/\n", "LibriSpeech/dev-clean-2/777/126732/777-126732.trans.txt\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0000.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0001.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0002.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0003.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0004.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0005.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0006.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0007.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0008.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0009.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0010.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0011.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0012.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0013.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0014.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0015.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0016.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0017.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0018.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0019.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0020.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0021.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0022.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0023.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0024.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0025.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0026.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0027.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0028.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0029.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0030.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0031.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0032.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0033.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0034.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0035.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0036.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0037.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0038.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0039.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0040.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0041.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0042.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0043.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0044.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0045.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0046.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0047.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0048.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0049.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0050.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0051.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0052.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0053.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0054.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0055.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0056.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0057.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0058.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0059.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0060.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0061.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0062.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0063.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0064.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0065.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0066.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0067.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0068.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0069.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0070.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0071.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0072.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0073.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0074.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0075.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0076.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0077.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0078.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0079.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0080.flac\n", "LibriSpeech/dev-clean-2/777/126732/777-126732-0081.flac\n", "LibriSpeech/dev-clean-2/174/\n", "LibriSpeech/dev-clean-2/174/168635/\n", "LibriSpeech/dev-clean-2/174/168635/174-168635.trans.txt\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0000.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0001.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0002.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0003.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0004.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0005.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0006.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0007.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0008.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0009.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0010.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0011.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0012.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0013.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0014.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0015.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0016.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0017.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0018.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0019.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0020.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0021.flac\n", "LibriSpeech/dev-clean-2/174/168635/174-168635-0022.flac\n", "LibriSpeech/dev-clean-2/3752/\n", "LibriSpeech/dev-clean-2/3752/4944/\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944.trans.txt\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0000.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0001.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0002.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0003.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0004.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0005.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0006.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0007.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0008.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0009.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0010.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0011.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0012.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0013.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0014.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0015.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0016.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0017.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0018.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0019.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0020.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0021.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0022.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0023.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0024.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0025.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0026.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0027.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0028.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0029.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0030.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0031.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0032.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0033.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0034.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0035.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0036.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0037.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0038.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0039.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0040.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0041.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0042.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0043.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0044.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0045.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0046.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0047.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0048.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0049.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0050.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0051.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0052.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0053.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0054.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0055.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0056.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0057.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0058.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0059.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0060.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0061.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0062.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0063.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0064.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0065.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0066.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0067.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0068.flac\n", "LibriSpeech/dev-clean-2/3752/4944/3752-4944-0069.flac\n", "local/download_and_untar.sh: Successfully downloaded and un-tarred /content/kaldi/egs/ac/training/corpus/dev-clean-2.tar.gz\n", "local/download_and_untar.sh: downloading data from www.openslr.org/resources/31/train-clean-5.tar.gz.  This may take some time, please be patient.\n", "--2022-08-17 12:36:40--  http://www.openslr.org/resources/31/train-clean-5.tar.gz\n", "Resolving www.openslr.org (www.openslr.org)... *************\n", "Connecting to www.openslr.org (www.openslr.org)|*************|:80... connected.\n", "HTTP request sent, awaiting response... 302 Found\n", "Location: https://us.openslr.org/resources/31/train-clean-5.tar.gz [following]\n", "--2022-08-17 12:36:40--  https://us.openslr.org/resources/31/train-clean-5.tar.gz\n", "Resolving us.openslr.org (us.openslr.org)... *************\n", "Connecting to us.openslr.org (us.openslr.org)|*************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 332954390 (318M) [application/x-gzip]\n", "Saving to: 'train-clean-5.tar.gz'\n", "\n", "train-clean-5.tar.g 100%[===================>] 317.53M   106MB/s    in 3.0s    \n", "\n", "2022-08-17 12:36:43 (106 MB/s) - 'train-clean-5.tar.gz' saved [332954390/332954390]\n", "\n", "/content/kaldi/egs/ac/training\n", "LibriSpeech/LICENSE.TXT\n", "LibriSpeech/README.TXT\n", "LibriSpeech/CHAPTERS.TXT\n", "LibriSpeech/SPEAKERS.TXT\n", "LibriSpeech/train-clean-5/\n", "LibriSpeech/train-clean-5/669/\n", "LibriSpeech/train-clean-5/669/129074/\n", "LibriSpeech/train-clean-5/669/129074/669-129074.trans.txt\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0000.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0001.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0002.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0003.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0004.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0005.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0006.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0007.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0008.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0009.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0010.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0011.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0012.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0013.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0014.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0015.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0016.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0017.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0018.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0019.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0020.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0021.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0022.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0023.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0024.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0025.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0026.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0027.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0028.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0029.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0030.flac\n", "LibriSpeech/train-clean-5/669/129074/669-129074-0031.flac\n", "LibriSpeech/train-clean-5/2136/\n", "LibriSpeech/train-clean-5/2136/5147/\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0036.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0037.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0038.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0039.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0040.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147.trans.txt\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0000.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0001.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0002.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0003.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0004.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0005.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0006.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0007.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0008.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0009.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0010.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0011.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0012.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0013.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0014.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0015.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0016.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0017.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0018.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0019.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0020.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0021.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0022.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0023.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0024.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0025.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0026.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0027.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0028.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0029.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0030.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0031.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0032.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0033.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0034.flac\n", "LibriSpeech/train-clean-5/2136/5147/2136-5147-0035.flac\n", "LibriSpeech/train-clean-5/1088/\n", "LibriSpeech/train-clean-5/1088/134318/\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318.trans.txt\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0000.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0001.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0002.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0003.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0004.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0005.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0006.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0007.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0008.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0009.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0010.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0011.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0012.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0013.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0014.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0015.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0016.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0017.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0018.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0019.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0020.flac\n", "LibriSpeech/train-clean-5/1088/134318/1088-134318-0021.flac\n", "LibriSpeech/train-clean-5/1088/134315/\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0024.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0025.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0026.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0027.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0028.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0029.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0030.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0031.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0032.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0033.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0034.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0035.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0036.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0037.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0038.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0039.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0040.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0041.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0042.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0043.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0044.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0045.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0046.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0047.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0048.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0049.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0050.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0051.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0052.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0053.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0054.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0055.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0056.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0057.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0058.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315.trans.txt\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0000.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0001.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0002.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0003.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0004.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0005.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0006.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0007.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0008.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0009.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0010.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0011.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0012.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0013.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0014.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0015.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0016.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0017.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0018.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0019.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0020.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0021.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0022.flac\n", "LibriSpeech/train-clean-5/1088/134315/1088-134315-0023.flac\n", "LibriSpeech/train-clean-5/163/\n", "LibriSpeech/train-clean-5/163/122947/\n", "LibriSpeech/train-clean-5/163/122947/163-122947.trans.txt\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0000.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0001.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0002.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0003.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0004.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0005.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0006.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0007.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0008.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0009.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0010.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0011.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0012.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0013.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0014.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0015.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0016.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0017.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0018.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0019.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0020.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0021.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0022.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0023.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0024.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0025.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0026.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0027.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0028.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0029.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0030.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0031.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0032.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0033.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0034.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0035.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0036.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0037.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0038.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0039.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0040.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0041.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0042.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0043.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0044.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0045.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0046.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0047.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0048.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0049.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0050.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0051.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0052.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0053.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0054.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0055.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0056.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0057.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0058.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0059.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0060.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0061.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0062.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0063.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0064.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0065.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0066.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0067.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0068.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0069.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0070.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0071.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0072.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0073.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0074.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0075.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0076.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0077.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0078.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0079.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0080.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0081.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0082.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0083.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0084.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0085.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0086.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0087.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0088.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0089.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0090.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0091.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0092.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0093.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0094.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0095.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0096.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0097.flac\n", "LibriSpeech/train-clean-5/163/122947/163-122947-0098.flac\n", "LibriSpeech/train-clean-5/3947/\n", "LibriSpeech/train-clean-5/3947/13262/\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0029.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0030.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0031.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0032.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0033.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0034.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0035.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0036.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0037.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0038.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0039.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0040.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0041.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0042.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0043.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0044.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0045.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0046.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0047.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0048.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0049.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0050.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0051.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262.trans.txt\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0000.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0001.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0002.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0003.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0004.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0005.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0006.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0007.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0008.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0009.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0010.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0011.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0012.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0013.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0014.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0015.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0016.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0017.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0018.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0019.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0020.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0021.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0022.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0023.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0024.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0025.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0026.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0027.flac\n", "LibriSpeech/train-clean-5/3947/13262/3947-13262-0028.flac\n", "LibriSpeech/train-clean-5/1898/\n", "LibriSpeech/train-clean-5/1898/145715/\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0029.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0030.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0031.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0032.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0033.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0034.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0035.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0036.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0037.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0038.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0039.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0040.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0041.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0042.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0043.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0044.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0045.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0046.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0047.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0048.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0049.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0050.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715.trans.txt\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0000.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0001.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0002.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0003.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0004.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0005.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0006.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0007.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0008.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0009.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0010.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0011.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0012.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0013.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0014.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0015.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0016.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0017.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0018.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0019.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0020.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0021.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0022.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0023.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0024.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0025.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0026.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0027.flac\n", "LibriSpeech/train-clean-5/1898/145715/1898-145715-0028.flac\n", "LibriSpeech/train-clean-5/4640/\n", "LibriSpeech/train-clean-5/4640/19188/\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0036.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0037.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0038.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0039.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0040.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188.trans.txt\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0000.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0001.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0002.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0003.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0004.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0005.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0006.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0007.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0008.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0009.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0010.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0011.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0012.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0013.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0014.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0015.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0016.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0017.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0018.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0019.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0020.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0021.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0022.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0023.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0024.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0025.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0026.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0027.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0028.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0029.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0030.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0031.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0032.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0033.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0034.flac\n", "LibriSpeech/train-clean-5/4640/19188/4640-19188-0035.flac\n", "LibriSpeech/train-clean-5/6848/\n", "LibriSpeech/train-clean-5/6848/252323/\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0036.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0037.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0038.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0039.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0040.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323.trans.txt\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0000.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0001.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0002.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0003.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0004.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0005.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0006.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0007.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0008.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0009.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0010.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0011.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0012.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0013.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0014.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0015.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0016.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0017.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0018.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0019.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0020.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0021.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0022.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0023.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0024.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0025.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0026.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0027.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0028.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0029.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0030.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0031.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0032.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0033.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0034.flac\n", "LibriSpeech/train-clean-5/6848/252323/6848-252323-0035.flac\n", "LibriSpeech/train-clean-5/4680/\n", "LibriSpeech/train-clean-5/4680/16042/\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0036.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0037.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0038.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0039.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042.trans.txt\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0000.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0001.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0002.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0003.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0004.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0005.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0006.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0007.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0008.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0009.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0010.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0011.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0012.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0013.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0014.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0015.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0016.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0017.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0018.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0019.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0020.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0021.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0022.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0023.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0024.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0025.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0026.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0027.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0028.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0029.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0030.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0031.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0032.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0033.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0034.flac\n", "LibriSpeech/train-clean-5/4680/16042/4680-16042-0035.flac\n", "LibriSpeech/train-clean-5/226/\n", "LibriSpeech/train-clean-5/226/122538/\n", "LibriSpeech/train-clean-5/226/122538/226-122538.trans.txt\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0000.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0001.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0002.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0003.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0004.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0005.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0006.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0007.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0008.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0009.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0010.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0011.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0012.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0013.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0014.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0015.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0016.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0017.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0018.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0019.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0020.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0021.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0022.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0023.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0024.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0025.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0026.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0027.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0028.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0029.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0030.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0031.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0032.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0033.flac\n", "LibriSpeech/train-clean-5/226/122538/226-122538-0034.flac\n", "LibriSpeech/train-clean-5/1970/\n", "LibriSpeech/train-clean-5/1970/28415/\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0036.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0037.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0038.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0039.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415.trans.txt\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0000.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0001.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0002.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0003.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0004.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0005.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0006.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0007.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0008.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0009.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0010.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0011.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0012.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0013.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0014.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0015.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0016.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0017.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0018.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0019.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0020.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0021.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0022.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0023.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0024.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0025.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0026.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0027.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0028.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0029.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0030.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0031.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0032.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0033.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0034.flac\n", "LibriSpeech/train-clean-5/1970/28415/1970-28415-0035.flac\n", "LibriSpeech/train-clean-5/1867/\n", "LibriSpeech/train-clean-5/1867/154075/\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0033.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0034.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0035.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0036.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0037.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0038.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0039.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0040.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0041.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0042.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0043.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0044.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0045.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075.trans.txt\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0000.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0001.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0002.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0003.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0004.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0005.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0006.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0007.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0008.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0009.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0010.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0011.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0012.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0013.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0014.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0015.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0016.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0017.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0018.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0019.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0020.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0021.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0022.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0023.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0024.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0025.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0026.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0027.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0028.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0029.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0030.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0031.flac\n", "LibriSpeech/train-clean-5/1867/154075/1867-154075-0032.flac\n", "LibriSpeech/train-clean-5/332/\n", "LibriSpeech/train-clean-5/332/128985/\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0006.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0007.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0008.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0009.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0010.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0011.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0012.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0013.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0014.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0015.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0016.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0017.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0018.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0019.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0020.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0021.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0022.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0023.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0024.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0025.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0026.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0027.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0028.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0029.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0030.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0031.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0032.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0033.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0034.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0035.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0036.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0037.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0038.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0039.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0040.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0041.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0042.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0043.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0044.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0045.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0046.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0047.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0048.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0049.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0050.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0051.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0052.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0053.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0054.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0055.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0056.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0057.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0058.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0059.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0060.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0061.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0062.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0063.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0064.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0065.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0066.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0067.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0068.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0069.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0070.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0071.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0072.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0073.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0074.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0075.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0076.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0077.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0078.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0079.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0080.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0081.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0082.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0083.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0084.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0085.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0086.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0087.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0088.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985.trans.txt\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0000.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0001.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0002.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0003.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0004.flac\n", "LibriSpeech/train-clean-5/332/128985/332-128985-0005.flac\n", "LibriSpeech/train-clean-5/2416/\n", "LibriSpeech/train-clean-5/2416/152139/\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0016.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0017.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0018.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0019.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0020.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0021.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0022.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0023.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0024.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0025.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0026.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0027.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0028.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0029.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0030.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0031.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0032.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0033.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0034.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0035.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0036.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0037.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0038.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0039.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0040.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0041.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0042.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0043.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0044.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0045.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0046.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0047.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0048.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0049.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0050.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0051.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0052.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0053.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0054.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0055.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0056.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0057.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0058.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0059.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0060.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0061.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0062.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0063.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0064.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0065.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0066.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0067.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0068.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0069.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0070.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0071.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0072.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139.trans.txt\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0000.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0001.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0002.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0003.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0004.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0005.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0006.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0007.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0008.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0009.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0010.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0011.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0012.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0013.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0014.flac\n", "LibriSpeech/train-clean-5/2416/152139/2416-152139-0015.flac\n", "LibriSpeech/train-clean-5/460/\n", "LibriSpeech/train-clean-5/460/172359/\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0012.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0013.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0014.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0015.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0016.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0017.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0018.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0019.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0020.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0021.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0022.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0023.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0024.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0025.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0026.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0027.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0028.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0029.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0030.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0031.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0032.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0033.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0034.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0035.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0036.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0037.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0038.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0039.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0040.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0041.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0042.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0043.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0044.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0045.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0046.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0047.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0048.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0049.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0050.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0051.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0052.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0053.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0054.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0055.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0056.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0057.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0058.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0059.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0060.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0061.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0062.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0063.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0064.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0065.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0066.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0067.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0068.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0069.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0070.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0071.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0072.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0073.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0074.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0075.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0076.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0077.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0078.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0079.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0080.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0081.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0082.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0083.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0084.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0085.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0086.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0087.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0088.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0089.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0090.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0091.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0092.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0093.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0094.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0095.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0096.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0097.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0098.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0099.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359.trans.txt\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0000.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0001.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0002.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0003.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0004.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0005.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0006.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0007.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0008.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0009.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0010.flac\n", "LibriSpeech/train-clean-5/460/172359/460-172359-0011.flac\n", "LibriSpeech/train-clean-5/19/\n", "LibriSpeech/train-clean-5/19/198/\n", "LibriSpeech/train-clean-5/19/198/19-198.trans.txt\n", "LibriSpeech/train-clean-5/19/198/19-198-0000.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0001.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0002.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0003.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0004.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0005.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0006.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0007.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0008.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0009.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0010.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0011.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0012.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0013.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0014.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0015.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0016.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0017.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0018.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0019.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0020.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0021.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0022.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0023.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0024.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0025.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0026.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0027.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0028.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0029.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0030.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0031.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0032.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0033.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0034.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0035.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0036.flac\n", "LibriSpeech/train-clean-5/19/198/19-198-0037.flac\n", "LibriSpeech/train-clean-5/3242/\n", "LibriSpeech/train-clean-5/3242/8112/\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0033.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0034.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0035.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0036.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0037.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0038.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0039.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0040.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0041.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0042.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0043.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0044.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0031.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112.trans.txt\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0000.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0001.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0002.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0003.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0004.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0005.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0006.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0007.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0008.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0009.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0010.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0011.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0012.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0013.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0014.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0015.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0016.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0017.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0018.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0019.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0020.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0021.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0022.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0023.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0024.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0025.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0026.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0027.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0028.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0029.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0030.flac\n", "LibriSpeech/train-clean-5/3242/8112/3242-8112-0032.flac\n", "LibriSpeech/train-clean-5/5789/\n", "LibriSpeech/train-clean-5/5789/70653/\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653.trans.txt\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0000.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0001.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0002.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0003.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0004.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0005.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0006.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0007.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0008.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0009.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0010.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0011.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0012.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0013.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0014.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0015.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0016.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0017.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0018.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0019.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0020.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0021.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0022.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0023.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0024.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0025.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0026.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0027.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0028.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0029.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0030.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0031.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0032.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0033.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0034.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0035.flac\n", "LibriSpeech/train-clean-5/5789/70653/5789-70653-0036.flac\n", "LibriSpeech/train-clean-5/6272/\n", "LibriSpeech/train-clean-5/6272/70171/\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0035.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0036.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0037.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0038.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0039.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0040.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0041.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171.trans.txt\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0000.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0001.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0002.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0003.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0004.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0005.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0006.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0007.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0008.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0009.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0010.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0011.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0012.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0013.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0014.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0015.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0016.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0017.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0018.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0019.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0020.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0021.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0022.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0023.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0024.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0025.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0026.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0027.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0028.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0029.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0030.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0031.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0032.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0033.flac\n", "LibriSpeech/train-clean-5/6272/70171/6272-70171-0034.flac\n", "LibriSpeech/train-clean-5/7312/\n", "LibriSpeech/train-clean-5/7312/92432/\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432.trans.txt\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0000.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0001.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0002.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0003.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0004.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0005.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0006.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0007.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0008.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0009.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0010.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0011.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0012.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0013.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0014.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0015.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0016.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0017.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0018.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0019.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0020.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0021.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0022.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0023.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0024.flac\n", "LibriSpeech/train-clean-5/7312/92432/7312-92432-0025.flac\n", "LibriSpeech/train-clean-5/3526/\n", "LibriSpeech/train-clean-5/3526/176653/\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0027.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0028.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0029.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0030.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0031.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0032.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0033.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0034.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0035.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0036.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0037.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0038.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0039.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0040.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0041.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0042.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0043.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0044.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0045.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0046.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0047.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0048.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0049.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0050.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0051.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0052.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0053.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0054.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653.trans.txt\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0000.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0001.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0002.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0003.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0004.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0005.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0006.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0007.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0008.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0009.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0010.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0011.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0012.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0013.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0014.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0015.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0016.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0017.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0018.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0019.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0020.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0021.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0022.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0023.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0024.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0025.flac\n", "LibriSpeech/train-clean-5/3526/176653/3526-176653-0026.flac\n", "LibriSpeech/train-clean-5/3664/\n", "LibriSpeech/train-clean-5/3664/178355/\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355.trans.txt\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0000.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0001.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0002.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0003.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0004.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0005.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0006.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0007.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0008.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0009.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0010.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0011.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0012.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0013.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0014.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0015.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0016.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0017.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0018.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0019.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0020.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0021.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0022.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0023.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0024.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0025.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0026.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0027.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0028.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0029.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0030.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0031.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0032.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0033.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0034.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0035.flac\n", "LibriSpeech/train-clean-5/3664/178355/3664-178355-0036.flac\n", "LibriSpeech/train-clean-5/7859/\n", "LibriSpeech/train-clean-5/7859/102519/\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0026.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0027.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0028.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0029.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0030.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0031.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0032.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0033.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0034.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0035.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0036.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0037.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0038.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0039.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0040.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0041.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0042.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0043.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0044.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0045.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0046.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0047.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0048.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0049.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0050.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0051.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0052.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0053.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0054.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0055.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0056.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519.trans.txt\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0000.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0001.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0002.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0003.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0004.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0005.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0006.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0007.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0008.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0009.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0010.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0011.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0012.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0013.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0014.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0015.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0016.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0017.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0018.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0019.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0020.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0021.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0022.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0023.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0024.flac\n", "LibriSpeech/train-clean-5/7859/102519/7859-102519-0025.flac\n", "LibriSpeech/train-clean-5/32/\n", "LibriSpeech/train-clean-5/32/21631/\n", "LibriSpeech/train-clean-5/32/21631/32-21631.trans.txt\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0000.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0001.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0002.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0003.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0004.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0005.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0006.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0007.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0008.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0009.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0010.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0011.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0012.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0013.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0014.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0015.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0016.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0017.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0018.flac\n", "LibriSpeech/train-clean-5/32/21631/32-21631-0019.flac\n", "LibriSpeech/train-clean-5/118/\n", "LibriSpeech/train-clean-5/118/47824/\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0007.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0008.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0009.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0010.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0011.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0012.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0013.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0014.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0015.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0016.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0017.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0018.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0019.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0020.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0021.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0022.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0023.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0024.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0025.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0026.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0027.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0028.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0029.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0030.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0031.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0032.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0033.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0034.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0035.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0036.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0037.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0038.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0039.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0040.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0041.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0042.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0043.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0044.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0045.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0046.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0047.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0048.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0049.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0050.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0051.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0052.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0053.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0054.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0055.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0056.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0057.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0058.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0059.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0060.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0061.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0062.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0063.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0064.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0065.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0066.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0067.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0068.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0069.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0070.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0071.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0072.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0073.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0074.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0075.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0076.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0077.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0078.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0079.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0080.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0081.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0082.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0083.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0084.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0085.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0086.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824.trans.txt\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0000.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0001.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0002.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0003.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0004.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0005.flac\n", "LibriSpeech/train-clean-5/118/47824/118-47824-0006.flac\n", "LibriSpeech/train-clean-5/1737/\n", "LibriSpeech/train-clean-5/1737/146161/\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161.trans.txt\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0000.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0001.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0002.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0003.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0004.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0005.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0006.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0007.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0008.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0009.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0010.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0011.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0012.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0013.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0014.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0015.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0016.flac\n", "LibriSpeech/train-clean-5/1737/146161/1737-146161-0017.flac\n", "LibriSpeech/train-clean-5/8629/\n", "LibriSpeech/train-clean-5/8629/261139/\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0013.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0014.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0015.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0016.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0017.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0018.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0019.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0020.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0021.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0022.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0023.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0024.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0025.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0026.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0027.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0028.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0029.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0030.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0031.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0032.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0033.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0034.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0035.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0036.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0037.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0038.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0039.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0040.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0041.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0042.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0043.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0044.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0045.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0046.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0047.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0048.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0049.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0050.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0051.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0052.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0053.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0054.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0055.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0056.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0057.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0058.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0059.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0060.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0061.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0062.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0063.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0064.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0065.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0066.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0067.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0068.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0069.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0070.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0071.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0072.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0073.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0074.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0075.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0076.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139.trans.txt\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0000.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0001.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0002.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0003.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0004.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0005.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0006.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0007.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0008.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0009.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0010.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0011.flac\n", "LibriSpeech/train-clean-5/8629/261139/8629-261139-0012.flac\n", "LibriSpeech/train-clean-5/7367/\n", "LibriSpeech/train-clean-5/7367/86737/\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0001.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0002.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0003.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0004.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0005.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0006.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0007.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0008.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0009.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0010.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0011.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0012.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0013.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0014.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0015.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0016.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0017.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0018.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0019.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0020.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0021.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0022.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0023.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0024.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0025.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0026.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0027.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0028.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0029.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0030.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0031.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0032.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0033.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0034.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0035.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0036.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0037.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0038.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0039.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0040.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0041.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0042.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0043.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0044.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0045.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0046.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0047.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0048.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0049.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0050.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0051.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0052.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0053.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0054.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0055.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0056.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0057.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0058.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0059.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0060.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0061.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0062.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0063.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0064.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0065.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0066.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0067.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0068.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0069.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0070.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0071.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0072.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0073.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0074.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0075.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0076.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0077.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0078.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0079.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0080.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0081.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0082.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0083.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0084.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0085.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0086.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0087.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0088.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0089.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0090.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0091.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0092.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0093.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0094.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0095.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0096.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0097.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0098.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0099.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0100.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0102.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0103.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0104.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0105.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0106.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0107.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0108.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0109.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0110.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0111.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0112.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0113.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0114.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0115.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0116.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0117.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0118.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0101.flac\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737.trans.txt\n", "LibriSpeech/train-clean-5/7367/86737/7367-86737-0000.flac\n", "local/download_and_untar.sh: Successfully downloaded and un-tarred /content/kaldi/egs/ac/training/corpus/train-clean-5.tar.gz\n", "Downloading file '3-gram.pruned.1e-7.arpa.gz' into 'corpus'...\n", "--2022-08-17 12:36:46--  http://www.openslr.org/resources/11/3-gram.pruned.1e-7.arpa.gz\n", "Resolving www.openslr.org (www.openslr.org)... *************\n", "Connecting to www.openslr.org (www.openslr.org)|*************|:80... connected.\n", "HTTP request sent, awaiting response... 302 Found\n", "Location: https://us.openslr.org/resources/11/3-gram.pruned.1e-7.arpa.gz [following]\n", "--2022-08-17 12:36:46--  https://us.openslr.org/resources/11/3-gram.pruned.1e-7.arpa.gz\n", "Resolving us.openslr.org (us.openslr.org)... *************\n", "Connecting to us.openslr.org (us.openslr.org)|*************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 34094057 (33M) [application/x-gzip]\n", "Saving to: 'corpus/3-gram.pruned.1e-7.arpa.gz'\n", "\n", "corpus/3-gram.prune 100%[===================>]  32.51M  94.4MB/s    in 0.3s    \n", "\n", "2022-08-17 12:36:46 (94.4 MB/s) - 'corpus/3-gram.pruned.1e-7.arpa.gz' saved [34094057/34094057]\n", "\n", "Downloading file '3-gram.pruned.3e-7.arpa.gz' into 'corpus'...\n", "--2022-08-17 12:36:46--  http://www.openslr.org/resources/11/3-gram.pruned.3e-7.arpa.gz\n", "Resolving www.openslr.org (www.openslr.org)... *************\n", "Connecting to www.openslr.org (www.openslr.org)|*************|:80... connected.\n", "HTTP request sent, awaiting response... 302 Found\n", "Location: https://us.openslr.org/resources/11/3-gram.pruned.3e-7.arpa.gz [following]\n", "--2022-08-17 12:36:46--  https://us.openslr.org/resources/11/3-gram.pruned.3e-7.arpa.gz\n", "Resolving us.openslr.org (us.openslr.org)... *************\n", "Connecting to us.openslr.org (us.openslr.org)|*************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 13654242 (13M) [application/x-gzip]\n", "Saving to: 'corpus/3-gram.pruned.3e-7.arpa.gz'\n", "\n", "corpus/3-gram.prune 100%[===================>]  13.02M  85.3MB/s    in 0.2s    \n", "\n", "2022-08-17 12:36:46 (85.3 MB/s) - 'corpus/3-gram.pruned.3e-7.arpa.gz' saved [13654242/13654242]\n", "\n", "Downloading file 'librispeech-vocab.txt' into 'corpus'...\n", "--2022-08-17 12:36:46--  http://www.openslr.org/resources/11/librispeech-vocab.txt\n", "Resolving www.openslr.org (www.openslr.org)... *************\n", "Connecting to www.openslr.org (www.openslr.org)|*************|:80... connected.\n", "HTTP request sent, awaiting response... 302 Found\n", "Location: https://us.openslr.org/resources/11/librispeech-vocab.txt [following]\n", "--2022-08-17 12:36:46--  https://us.openslr.org/resources/11/librispeech-vocab.txt\n", "Resolving us.openslr.org (us.openslr.org)... *************\n", "Connecting to us.openslr.org (us.openslr.org)|*************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 1737588 (1.7M) [text/plain]\n", "Saving to: 'corpus/librispeech-vocab.txt'\n", "\n", "corpus/librispeech- 100%[===================>]   1.66M  --.-KB/s    in 0.1s    \n", "\n", "2022-08-17 12:36:47 (15.7 MB/s) - 'corpus/librispeech-vocab.txt' saved [1737588/1737588]\n", "\n", "Downloading file 'librispeech-lexicon.txt' into 'corpus'...\n", "--2022-08-17 12:36:47--  http://www.openslr.org/resources/11/librispeech-lexicon.txt\n", "Resolving www.openslr.org (www.openslr.org)... *************\n", "Connecting to www.openslr.org (www.openslr.org)|*************|:80... connected.\n", "HTTP request sent, awaiting response... 302 Found\n", "Location: https://us.openslr.org/resources/11/librispeech-lexicon.txt [following]\n", "--2022-08-17 12:36:47--  https://us.openslr.org/resources/11/librispeech-lexicon.txt\n", "Resolving us.openslr.org (us.openslr.org)... *************\n", "Connecting to us.openslr.org (us.openslr.org)|*************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 5627653 (5.4M) [text/plain]\n", "Saving to: 'corpus/librispeech-lexicon.txt'\n", "\n", "corpus/librispeech- 100%[===================>]   5.37M  --.-KB/s    in 0.08s   \n", "\n", "2022-08-17 12:36:47 (68.0 MB/s) - 'corpus/librispeech-lexicon.txt' saved [5627653/5627653]\n", "\n", "utils/validate_data_dir.sh: Successfully validated data-directory data/train\n", "local/data_prep.sh: successfully prepared data in data/train\n", "utils/validate_data_dir.sh: Successfully validated data-directory data/test\n", "local/data_prep.sh: successfully prepared data in data/test\n", "Preparing phone lists and lexicon\n", "utils/prepare_lang.sh data/local/dict <UNK> data/local/lang data/lang\n", "Checking data/local/dict/silence_phones.txt ...\n", "--> reading data/local/dict/silence_phones.txt\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> data/local/dict/silence_phones.txt is OK\n", "\n", "Checking data/local/dict/optional_silence.txt ...\n", "--> reading data/local/dict/optional_silence.txt\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> data/local/dict/optional_silence.txt is OK\n", "\n", "Checking data/local/dict/nonsilence_phones.txt ...\n", "--> reading data/local/dict/nonsilence_phones.txt\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> data/local/dict/nonsilence_phones.txt is OK\n", "\n", "Checking disjoint: silence_phones.txt, nonsilence_phones.txt\n", "--> disjoint property is OK.\n", "\n", "Checking data/local/dict/lexicon.txt\n", "--> reading data/local/dict/lexicon.txt\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> data/local/dict/lexicon.txt is OK\n", "\n", "Checking data/local/dict/extra_questions.txt ...\n", "--> data/local/dict/extra_questions.txt is empty (this is OK)\n", "--> SUCCESS [validating dictionary directory data/local/dict]\n", "\n", "**Creating data/local/dict/lexiconp.txt from data/local/dict/lexicon.txt\n", "fstaddselfloops data/lang/phones/wdisambig_phones.int data/lang/phones/wdisambig_words.int \n", "prepare_lang.sh: validating output directory\n", "utils/validate_lang.pl data/lang\n", "Checking existence of separator file\n", "separator file data/lang/subword_separator.txt is empty or does not exist, deal in word case.\n", "Checking data/lang/phones.txt ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> data/lang/phones.txt is OK\n", "\n", "Checking words.txt: #0 ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> data/lang/words.txt is OK\n", "\n", "Checking disjoint: silence.txt, nonsilence.txt, disambig.txt ...\n", "--> silence.txt and nonsilence.txt are disjoint\n", "--> silence.txt and disambig.txt are disjoint\n", "--> disambig.txt and nonsilence.txt are disjoint\n", "--> disjoint property is OK\n", "\n", "Checking sumation: silence.txt, nonsilence.txt, disambig.txt ...\n", "--> found no unexplainable phones in phones.txt\n", "\n", "Checking data/lang/phones/context_indep.{txt, int, csl} ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> 10 entry/entries in data/lang/phones/context_indep.txt\n", "--> data/lang/phones/context_indep.int corresponds to data/lang/phones/context_indep.txt\n", "--> data/lang/phones/context_indep.csl corresponds to data/lang/phones/context_indep.txt\n", "--> data/lang/phones/context_indep.{txt, int, csl} are OK\n", "\n", "Checking data/lang/phones/nonsilence.{txt, int, csl} ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> 156 entry/entries in data/lang/phones/nonsilence.txt\n", "--> data/lang/phones/nonsilence.int corresponds to data/lang/phones/nonsilence.txt\n", "--> data/lang/phones/nonsilence.csl corresponds to data/lang/phones/nonsilence.txt\n", "--> data/lang/phones/nonsilence.{txt, int, csl} are OK\n", "\n", "Checking data/lang/phones/silence.{txt, int, csl} ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> 10 entry/entries in data/lang/phones/silence.txt\n", "--> data/lang/phones/silence.int corresponds to data/lang/phones/silence.txt\n", "--> data/lang/phones/silence.csl corresponds to data/lang/phones/silence.txt\n", "--> data/lang/phones/silence.{txt, int, csl} are OK\n", "\n", "Checking data/lang/phones/optional_silence.{txt, int, csl} ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> 1 entry/entries in data/lang/phones/optional_silence.txt\n", "--> data/lang/phones/optional_silence.int corresponds to data/lang/phones/optional_silence.txt\n", "--> data/lang/phones/optional_silence.csl corresponds to data/lang/phones/optional_silence.txt\n", "--> data/lang/phones/optional_silence.{txt, int, csl} are OK\n", "\n", "Checking data/lang/phones/disambig.{txt, int, csl} ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> 21 entry/entries in data/lang/phones/disambig.txt\n", "--> data/lang/phones/disambig.int corresponds to data/lang/phones/disambig.txt\n", "--> data/lang/phones/disambig.csl corresponds to data/lang/phones/disambig.txt\n", "--> data/lang/phones/disambig.{txt, int, csl} are OK\n", "\n", "Checking data/lang/phones/roots.{txt, int} ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> 41 entry/entries in data/lang/phones/roots.txt\n", "--> data/lang/phones/roots.int corresponds to data/lang/phones/roots.txt\n", "--> data/lang/phones/roots.{txt, int} are OK\n", "\n", "Checking data/lang/phones/sets.{txt, int} ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> 41 entry/entries in data/lang/phones/sets.txt\n", "--> data/lang/phones/sets.int corresponds to data/lang/phones/sets.txt\n", "--> data/lang/phones/sets.{txt, int} are OK\n", "\n", "Checking data/lang/phones/extra_questions.{txt, int} ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> 9 entry/entries in data/lang/phones/extra_questions.txt\n", "--> data/lang/phones/extra_questions.int corresponds to data/lang/phones/extra_questions.txt\n", "--> data/lang/phones/extra_questions.{txt, int} are OK\n", "\n", "Checking data/lang/phones/word_boundary.{txt, int} ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> 166 entry/entries in data/lang/phones/word_boundary.txt\n", "--> data/lang/phones/word_boundary.int corresponds to data/lang/phones/word_boundary.txt\n", "--> data/lang/phones/word_boundary.{txt, int} are OK\n", "\n", "Checking optional_silence.txt ...\n", "--> reading data/lang/phones/optional_silence.txt\n", "--> data/lang/phones/optional_silence.txt is OK\n", "\n", "Checking disambiguation symbols: #0 and #1\n", "--> data/lang/phones/disambig.txt has \"#0\" and \"#1\"\n", "--> data/lang/phones/disambig.txt is OK\n", "\n", "Checking topo ...\n", "\n", "Checking word_boundary.txt: silence.txt, nonsilence.txt, disambig.txt ...\n", "--> data/lang/phones/word_boundary.txt doesn't include disambiguation symbols\n", "--> data/lang/phones/word_boundary.txt is the union of nonsilence.txt and silence.txt\n", "--> data/lang/phones/word_boundary.txt is OK\n", "\n", "Checking word-level disambiguation symbols...\n", "--> data/lang/phones/wdisambig.txt exists (newer prepare_lang.sh)\n", "Checking word_boundary.int and disambig.int\n", "--> generating a 27 word/subword sequence\n", "--> resulting phone sequence from L.fst corresponds to the word sequence\n", "--> L.fst is OK\n", "--> generating a 73 word/subword sequence\n", "--> resulting phone sequence from L_disambig.fst corresponds to the word sequence\n", "--> L_disambig.fst is OK\n", "\n", "Checking data/lang/oov.{txt, int} ...\n", "--> text seems to be UTF-8 or ASCII, checking whitespaces\n", "--> text contains only allowed whitespaces\n", "--> 1 entry/entries in data/lang/oov.txt\n", "--> data/lang/oov.int corresponds to data/lang/oov.txt\n", "--> data/lang/oov.{txt, int} are OK\n", "\n", "--> data/lang/L.fst is olabel sorted\n", "--> data/lang/L_disambig.fst is olabel sorted\n", "--> SUCCESS [validating lang directory data/lang]\n", "steps/make_mfcc.sh --cmd run.pl --nj 2 data/train exp/make_mfcc/train\n", "utils/validate_data_dir.sh: Successfully validated data-directory data/train\n", "steps/make_mfcc.sh: [info]: no segments file exists: assuming wav.scp indexed by utterance.\n", "steps/make_mfcc.sh: Succeeded creating MFCC features for train\n", "steps/compute_cmvn_stats.sh data/train exp/make_mfcc/train\n", "Succeeded creating CMVN stats for train\n", "steps/train_mono.sh --nj 2 --cmd run.pl data/train data/lang exp/mono\n", "steps/train_mono.sh: Initializing monophone system.\n", "steps/train_mono.sh: Compiling training graphs\n", "steps/train_mono.sh: Aligning data equally (pass 0)\n", "steps/train_mono.sh: Pass 1\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 2\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 3\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 4\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 5\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 6\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 7\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 8\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 9\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 10\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 11\n", "steps/train_mono.sh: Pass 12\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 13\n", "steps/train_mono.sh: Pass 14\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 15\n", "steps/train_mono.sh: Pass 16\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 17\n", "steps/train_mono.sh: Pass 18\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 19\n", "steps/train_mono.sh: Pass 20\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 21\n", "steps/train_mono.sh: Pass 22\n", "steps/train_mono.sh: Pass 23\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 24\n", "steps/train_mono.sh: Pass 25\n", "steps/train_mono.sh: Pass 26\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 27\n", "steps/train_mono.sh: Pass 28\n", "steps/train_mono.sh: Pass 29\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 30\n", "steps/train_mono.sh: Pass 31\n", "steps/train_mono.sh: Pass 32\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 33\n", "steps/train_mono.sh: Pass 34\n", "steps/train_mono.sh: Pass 35\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 36\n", "steps/train_mono.sh: Pass 37\n", "steps/train_mono.sh: Pass 38\n", "steps/train_mono.sh: Aligning data\n", "steps/train_mono.sh: Pass 39\n", "steps/diagnostic/analyze_alignments.sh --cmd run.pl data/lang exp/mono\n", "steps/diagnostic/analyze_alignments.sh: see stats in exp/mono/log/analyze_alignments.log\n", "896 warnings in exp/mono/log/acc.*.*.log\n", "7156 warnings in exp/mono/log/align.*.*.log\n", "exp/mono: nj=2 align prob=-247.97 over 5.21h [retry=17.3%, fail=1.6%] states=127 gauss=1000\n", "steps/train_mono.sh: Done training monophone system in exp/mono\n", "steps/align_si.sh --nj 2 --cmd run.pl data/train data/lang exp/mono exp/mono_ali\n", "steps/align_si.sh: feature type is delta\n", "steps/align_si.sh: aligning data in data/train using model from exp/mono, putting alignments in exp/mono_ali\n", "steps/diagnostic/analyze_alignments.sh --cmd run.pl data/lang exp/mono_ali\n", "steps/diagnostic/analyze_alignments.sh: see stats in exp/mono_ali/log/analyze_alignments.log\n", "steps/align_si.sh: done aligning data.\n", "steps/train_lda_mllt.sh --cmd run.pl 2000 10000 data/train data/lang exp/mono_ali exp/tri1\n", "steps/train_lda_mllt.sh: Accumulating LDA statistics.\n", "steps/train_lda_mllt.sh: Accumulating tree stats\n", "steps/train_lda_mllt.sh: Getting questions for tree clustering.\n", "steps/train_lda_mllt.sh: Building the tree\n", "steps/train_lda_mllt.sh: Initializing the model\n", "steps/train_lda_mllt.sh: Converting alignments from exp/mono_ali to use current tree\n", "steps/train_lda_mllt.sh: Compiling graphs of transcripts\n", "Training pass 1\n", "Training pass 2\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 3\n", "Training pass 4\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 5\n", "Training pass 6\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 7\n", "Training pass 8\n", "Training pass 9\n", "Training pass 10\n", "Aligning data\n", "Training pass 11\n", "Training pass 12\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 13\n", "Training pass 14\n", "Training pass 15\n", "Training pass 16\n", "Training pass 17\n", "Training pass 18\n", "Training pass 19\n", "Training pass 20\n", "Aligning data\n", "Training pass 21\n", "Training pass 22\n", "Training pass 23\n", "Training pass 24\n", "Training pass 25\n", "Training pass 26\n", "Training pass 27\n", "Training pass 28\n", "Training pass 29\n", "Training pass 30\n", "Aligning data\n", "Training pass 31\n", "Training pass 32\n", "Training pass 33\n", "Training pass 34\n", "steps/diagnostic/analyze_alignments.sh --cmd run.pl data/lang exp/tri1\n", "steps/diagnostic/analyze_alignments.sh: see stats in exp/tri1/log/analyze_alignments.log\n", "25 warnings in exp/tri1/log/lda_acc.*.log\n", "375 warnings in exp/tri1/log/acc.*.*.log\n", "1 warnings in exp/tri1/log/build_tree.log\n", "5 warnings in exp/tri1/log/update.*.log\n", "30 warnings in exp/tri1/log/init_model.log\n", "274 warnings in exp/tri1/log/align.*.*.log\n", "exp/tri1: nj=2 align prob=-46.98 over 5.29h [retry=3.0%, fail=0.3%] states=1608 gauss=10025 tree-impr=4.06 lda-sum=10.11 mllt:impr,logdet=0.91,1.33\n", "steps/train_lda_mllt.sh: Done training system with LDA+MLLT features in exp/tri1\n", "steps/align_si.sh --nj 2 --cmd run.pl data/train data/lang exp/tri1 exp/tri1_ali\n", "steps/align_si.sh: feature type is lda\n", "steps/align_si.sh: aligning data in data/train using model from exp/tri1, putting alignments in exp/tri1_ali\n", "steps/diagnostic/analyze_alignments.sh --cmd run.pl data/lang exp/tri1_ali\n", "steps/diagnostic/analyze_alignments.sh: see stats in exp/tri1_ali/log/analyze_alignments.log\n", "steps/align_si.sh: done aligning data.\n", "steps/train_lda_mllt.sh --cmd run.pl 2500 15000 data/train data/lang exp/tri1_ali exp/tri2\n", "steps/train_lda_mllt.sh: Accumulating LDA statistics.\n", "steps/train_lda_mllt.sh: Accumulating tree stats\n", "steps/train_lda_mllt.sh: Getting questions for tree clustering.\n", "steps/train_lda_mllt.sh: Building the tree\n", "steps/train_lda_mllt.sh: Initializing the model\n", "steps/train_lda_mllt.sh: Converting alignments from exp/tri1_ali to use current tree\n", "steps/train_lda_mllt.sh: Compiling graphs of transcripts\n", "Training pass 1\n", "Training pass 2\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 3\n", "Training pass 4\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 5\n", "Training pass 6\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 7\n", "Training pass 8\n", "Training pass 9\n", "Training pass 10\n", "Aligning data\n", "Training pass 11\n", "Training pass 12\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 13\n", "Training pass 14\n", "Training pass 15\n", "Training pass 16\n", "Training pass 17\n", "Training pass 18\n", "Training pass 19\n", "Training pass 20\n", "Aligning data\n", "Training pass 21\n", "Training pass 22\n", "Training pass 23\n", "Training pass 24\n", "Training pass 25\n", "Training pass 26\n", "Training pass 27\n", "Training pass 28\n", "Training pass 29\n", "Training pass 30\n", "Aligning data\n", "Training pass 31\n", "Training pass 32\n", "Training pass 33\n", "Training pass 34\n", "steps/diagnostic/analyze_alignments.sh --cmd run.pl data/lang exp/tri2\n", "steps/diagnostic/analyze_alignments.sh: see stats in exp/tri2/log/analyze_alignments.log\n", "125 warnings in exp/tri2/log/align.*.*.log\n", "16 warnings in exp/tri2/log/update.*.log\n", "4 warnings in exp/tri2/log/lda_acc.*.log\n", "50 warnings in exp/tri2/log/init_model.log\n", "1 warnings in exp/tri2/log/build_tree.log\n", "111 warnings in exp/tri2/log/acc.*.*.log\n", "exp/tri2: nj=2 align prob=-48.07 over 5.29h [retry=2.1%, fail=0.2%] states=2072 gauss=15029 tree-impr=5.29 lda-sum=22.84 mllt:impr,logdet=0.97,1.48\n", "steps/train_lda_mllt.sh: Done training system with LDA+MLLT features in exp/tri2\n", "steps/align_si.sh --nj 2 --cmd run.pl data/train data/lang exp/tri2 exp/tri2_ali\n", "steps/align_si.sh: feature type is lda\n", "steps/align_si.sh: aligning data in data/train using model from exp/tri2, putting alignments in exp/tri2_ali\n", "steps/diagnostic/analyze_alignments.sh --cmd run.pl data/lang exp/tri2_ali\n", "steps/diagnostic/analyze_alignments.sh: see stats in exp/tri2_ali/log/analyze_alignments.log\n", "steps/align_si.sh: done aligning data.\n", "steps/train_lda_mllt.sh --cmd run.pl 2500 20000 data/train data/lang exp/tri2_ali exp/tri3\n", "steps/train_lda_mllt.sh: Accumulating LDA statistics.\n", "steps/train_lda_mllt.sh: Accumulating tree stats\n", "steps/train_lda_mllt.sh: Getting questions for tree clustering.\n", "steps/train_lda_mllt.sh: Building the tree\n", "steps/train_lda_mllt.sh: Initializing the model\n", "steps/train_lda_mllt.sh: Converting alignments from exp/tri2_ali to use current tree\n", "steps/train_lda_mllt.sh: Compiling graphs of transcripts\n", "Training pass 1\n", "Training pass 2\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 3\n", "Training pass 4\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 5\n", "Training pass 6\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 7\n", "Training pass 8\n", "Training pass 9\n", "Training pass 10\n", "Aligning data\n", "Training pass 11\n", "Training pass 12\n", "steps/train_lda_mllt.sh: Estimating MLLT\n", "Training pass 13\n", "Training pass 14\n", "Training pass 15\n", "Training pass 16\n", "Training pass 17\n", "Training pass 18\n", "Training pass 19\n", "Training pass 20\n", "Aligning data\n", "Training pass 21\n", "Training pass 22\n", "Training pass 23\n", "Training pass 24\n", "Training pass 25\n", "Training pass 26\n", "Training pass 27\n", "Training pass 28\n", "Training pass 29\n", "Training pass 30\n", "Aligning data\n", "Training pass 31\n", "Training pass 32\n", "Training pass 33\n", "Training pass 34\n", "steps/diagnostic/analyze_alignments.sh --cmd run.pl data/lang exp/tri3\n", "steps/diagnostic/analyze_alignments.sh: see stats in exp/tri3/log/analyze_alignments.log\n", "13 warnings in exp/tri3/log/update.*.log\n", "31 warnings in exp/tri3/log/init_model.log\n", "102 warnings in exp/tri3/log/acc.*.*.log\n", "3 warnings in exp/tri3/log/lda_acc.*.log\n", "1 warnings in exp/tri3/log/build_tree.log\n", "91 warnings in exp/tri3/log/align.*.*.log\n", "exp/tri3: nj=2 align prob=-47.83 over 5.29h [retry=1.3%, fail=0.2%] states=2080 gauss=20029 tree-impr=5.55 lda-sum=24.57 mllt:impr,logdet=1.02,1.66\n", "steps/train_lda_mllt.sh: Done training system with LDA+MLLT features in exp/tri3\n", "steps/align_si.sh --nj 2 --cmd run.pl data/train data/lang exp/tri3 exp/tri3_ali\n", "steps/align_si.sh: feature type is lda\n", "steps/align_si.sh: aligning data in data/train using model from exp/tri3, putting alignments in exp/tri3_ali\n", "steps/diagnostic/analyze_alignments.sh --cmd run.pl data/lang exp/tri3_ali\n", "steps/diagnostic/analyze_alignments.sh: see stats in exp/tri3_ali/log/analyze_alignments.log\n", "steps/align_si.sh: done aligning data.\n"]}]}, {"cell_type": "code", "source": ["!sed -i 's:--nj 10:--nj 2:g' local/chain/run_tdnn.sh\n", "!sed -i 's:steps/nnet3/chain/train.py:steps/nnet3/chain/train.py --use-gpu false:g' local/chain/run_tdnn.sh\n", "!cat local/chain/run_tdnn.sh\n", "!bash local/chain/run_tdnn.sh"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sWKGX5f-fpKJ", "outputId": "26dcf7dc-a08b-46bb-d72d-6c52397c287d"}, "execution_count": 18, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["#!/bin/bash\n", "\n", "# Set -e here so that we catch if any executable fails immediately\n", "set -euo pipefail\n", "\n", "# (some of which are also used in this script directly).\n", "stage=-1\n", "decode_nj=10\n", "train_set=train\n", "gmm=tri3\n", "nnet3_affix=\n", "suffix=\n", "\n", "# The rest are configs specific to this script.  Most of the parameters\n", "# are just hardcoded at this level, in the commands below.\n", "affix=   # affix for the TDNN directory name\n", "tree_affix=\n", "train_stage=-10\n", "get_egs_stage=-10\n", "decode_iter=\n", "\n", "# training options\n", "# training chunk-options\n", "chunk_width=140,100,160\n", "common_egs_dir=\n", "xent_regularize=0.1\n", "dropout_schedule='0,0@0.20,0.5@0.50,0'\n", "\n", "# training options\n", "srand=0\n", "remove_egs=true\n", "\n", "# End configuration section.\n", "echo \"$0 $@\"  # Print the command line for logging\n", "\n", ". ./cmd.sh\n", ". ./path.sh\n", ". ./utils/parse_options.sh\n", "\n", "# Problem: We have removed the \"train_\" prefix of our training set in\n", "# the alignment directory names! Bad!\n", "gmm_dir=exp/$gmm\n", "ali_dir=exp/${gmm}_ali\n", "tree_dir=exp/chain${suffix}/tree${tree_affix:+_$tree_affix}\n", "lang=data/lang_chain${suffix}\n", "lat_dir=exp/chain${suffix}/${gmm}_${train_set}_lats\n", "dir=exp/chain${suffix}/tdnn${affix}\n", "train_data_dir=data/${train_set}\n", "\n", "for f in $gmm_dir/final.mdl $train_data_dir/feats.scp $ali_dir/ali.1.gz; do\n", "  [ ! -f $f ] && echo \"$0: expected file $f to exist\" && exit 1\n", "done\n", "\n", "if [ $stage -le 9 ]; then\n", "    local/chain/run_ivector_common.sh \\\n", "                          --train-set ${train_set} \\\n", "                          --gmm ${gmm} \\\n", "                          --suffix \"${suffix}\"\n", "fi\n", "\n", "if [ $stage -le 10 ]; then\n", "  echo \"$0: creating lang directory $lang with chain-type topology\"\n", "  rm -rf $lang\n", "  cp -r data/lang $lang\n", "  silphonelist=$(cat $lang/phones/silence.csl) \n", "  nonsilphonelist=$(cat $lang/phones/nonsilence.csl) \n", "  steps/nnet3/chain/gen_topo.py $nonsilphonelist $silphonelist >$lang/topo\n", "fi\n", "\n", "if [ $stage -le 11 ]; then\n", "    steps/align_fmllr_lats.sh --nj 20 --cmd \"$train_cmd\" ${train_data_dir} \\\n", "         data/lang $gmm_dir $lat_dir\n", "fi\n", "\n", "if [ $stage -le 12 ]; then\n", "  steps/nnet3/chain/build_tree.sh \\\n", "    --frame-subsampling-factor 3 \\\n", "    --context-opts \"--context-width=2 --central-position=1\" \\\n", "    --cmd \"$train_cmd\" 2500 ${train_data_dir} \\\n", "    $lang $ali_dir $tree_dir\n", "fi\n", "\n", "if [ $stage -le 13 ]; then\n", "  echo \"$0: creating neural net configs using the xconfig parser\";\n", "\n", "  num_targets=$(tree-info $tree_dir/tree | grep num-pdfs | awk '{print $2}')\n", "  learning_rate_factor=$(echo \"print (0.5/$xent_regularize)\" | python)\n", "\n", "  affine_opts=\"l2-regularize=0.008 dropout-proportion=0.0 dropout-per-dim=true dropout-per-dim-continuous=true\"\n", "  tdnnf_opts=\"l2-regularize=0.008 dropout-proportion=0.0 bypass-scale=0.75\"\n", "  linear_opts=\"l2-regularize=0.008 orthonormal-constraint=-1.0\"\n", "  prefinal_opts=\"l2-regularize=0.008\"\n", "  output_opts=\"l2-regularize=0.002\"\n", "\n", "  mkdir -p $dir/configs\n", "  cat <<EOF > $dir/configs/network.xconfig\n", "\n", "  input dim=40 name=ivector\n", "  input dim=40 name=input\n", "\n", "  idct-layer name=idct input=input dim=40 cepstral-lifter=22 affine-transform-file=$dir/configs/idct.mat\n", "  batchnorm-component name=batchnorm0 input=idct\n", "  spec-augment-layer name=spec-augment freq-max-proportion=0.5 time-zeroed-proportion=0.2 time-mask-max-frames=20\n", "  delta-layer name=delta input=spec-augment\n", "\n", "  no-op-component name=input2 input=Append(delta, ReplaceIndex(ivector, t, 0))\n", "\n", "  # the first splicing is moved before the lda layer, so no splicing here\n", "  relu-batchnorm-dropout-layer name=tdnn1 $affine_opts dim=512 input=input2\n", "  tdnnf-layer name=tdnnf2 $tdnnf_opts dim=512 bottleneck-dim=96 time-stride=1\n", "  tdnnf-layer name=tdnnf3 $tdnnf_opts dim=512 bottleneck-dim=96 time-stride=1\n", "  tdnnf-layer name=tdnnf4 $tdnnf_opts dim=512 bottleneck-dim=96 time-stride=1\n", "  tdnnf-layer name=tdnnf5 $tdnnf_opts dim=512 bottleneck-dim=96 time-stride=0\n", "  tdnnf-layer name=tdnnf6 $tdnnf_opts dim=512 bottleneck-dim=96 time-stride=3\n", "  tdnnf-layer name=tdnnf7 $tdnnf_opts dim=512 bottleneck-dim=96 time-stride=3\n", "  tdnnf-layer name=tdnnf8 $tdnnf_opts dim=512 bottleneck-dim=96 time-stride=3\n", "  tdnnf-layer name=tdnnf9 $tdnnf_opts dim=512 bottleneck-dim=96 time-stride=3\n", "  tdnnf-layer name=tdnnf10 $tdnnf_opts dim=512 bottleneck-dim=96 time-stride=3\n", "  tdnnf-layer name=tdnnf11 $tdnnf_opts dim=512 bottleneck-dim=96 time-stride=3\n", "  tdnnf-layer name=tdnnf12 $tdnnf_opts dim=512 bottleneck-dim=96 time-stride=3\n", "  linear-component name=prefinal-l dim=192 $linear_opts\n", "\n", "  ## adding the layers for chain branch\n", "  prefinal-layer name=prefinal-chain input=prefinal-l $prefinal_opts small-dim=192 big-dim=512\n", "  output-layer name=output include-log-softmax=false dim=$num_targets $output_opts\n", "\n", "  # adding the layers for xent branch\n", "  prefinal-layer name=prefinal-xent input=prefinal-l $prefinal_opts small-dim=192 big-dim=512\n", "  output-layer name=output-xent dim=$num_targets learning-rate-factor=$learning_rate_factor $output_opts\n", "\n", "EOF\n", "  steps/nnet3/xconfig_to_configs.py --xconfig-file $dir/configs/network.xconfig --config-dir $dir/configs/\n", "fi\n", "\n", "if [ $stage -le 14 ]; then\n", "  steps/nnet3/chain/train.py --use-gpu false --stage $train_stage \\\n", "    --cmd \"$cuda_cmd\" \\\n", "    --feat.online-ivector-dir exp/chain${suffix}/ivectors_${train_set} \\\n", "    --feat.cmvn-opts \"--norm-means=false --norm-vars=false\" \\\n", "    --chain.xent-regularize $xent_regularize \\\n", "    --chain.leaky-hmm-coefficient 0.1 \\\n", "    --chain.l2-regularize 0.0 \\\n", "    --chain.apply-deriv-weights false \\\n", "    --chain.lm-opts=\"--num-extra-lm-states=2000\" \\\n", "    --egs.cmd \"$get_egs_cmd\" \\\n", "    --egs.dir \"$common_egs_dir\" \\\n", "    --egs.stage $get_egs_stage \\\n", "    --egs.opts \"--frames-overlap-per-eg 0 --constrained false\" \\\n", "    --egs.chunk-width $chunk_width \\\n", "    --trainer.dropout-schedule $dropout_schedule \\\n", "    --trainer.add-option=\"--optimization.memory-compression-level=2\" \\\n", "    --trainer.num-chunk-per-minibatch 64 \\\n", "    --trainer.frames-per-iter 2500000 \\\n", "    --trainer.num-epochs 20 \\\n", "    --trainer.optimization.num-jobs-initial 1 \\\n", "    --trainer.optimization.num-jobs-final 1 \\\n", "    --trainer.optimization.initial-effective-lrate 0.001 \\\n", "    --trainer.optimization.final-effective-lrate 0.0001 \\\n", "    --trainer.max-param-change 2.0 \\\n", "    --cleanup.remove-egs $remove_egs \\\n", "    --feat-dir $train_data_dir \\\n", "    --tree-dir $tree_dir \\\n", "    --lat-dir $lat_dir \\\n", "    --dir $dir  || exit 1;\n", "fi\n", "local/chain/run_tdnn.sh \n", "local/chain/run_ivector_common.sh: computing a subset of data to train the diagonal UBM.\n", "utils/data/subset_data_dir.sh: reducing #utt from 1519 to 379\n", "local/chain/run_ivector_common.sh: computing a PCA transform from the data.\n", "steps/online/nnet2/get_pca_transform.sh --cmd run.pl --splice-opts --left-context=3 --right-context=3 --max-utts 10000 --subsample 2 exp/chain/diag_ubm/train_subset exp/chain/pca_transform\n", "Done estimating PCA transform in exp/chain/pca_transform\n", "local/chain/run_ivector_common.sh: training the diagonal UBM.\n", "steps/online/nnet2/train_diag_ubm.sh --cmd run.pl --nj 10 --num-frames 700000 --num-threads 8 exp/chain/diag_ubm/train_subset 512 exp/chain/pca_transform exp/chain/diag_ubm\n", "steps/online/nnet2/train_diag_ubm.sh: Directory exp/chain/diag_ubm already exists. Backing up diagonal UBM in exp/chain/diag_ubm/backup.WXg\n", "steps/online/nnet2/train_diag_ubm.sh: initializing model from E-M in memory, \n", "steps/online/nnet2/train_diag_ubm.sh: starting from 256 Gaussians, reaching 512;\n", "steps/online/nnet2/train_diag_ubm.sh: for 20 iterations, using at most 700000 frames of data\n", "Getting Gaussian-selection info\n", "steps/online/nnet2/train_diag_ubm.sh: will train for 4 iterations, in parallel over\n", "steps/online/nnet2/train_diag_ubm.sh: 10 machines, parallelized with 'run.pl'\n", "steps/online/nnet2/train_diag_ubm.sh: Training pass 0\n", "steps/online/nnet2/train_diag_ubm.sh: Training pass 1\n", "steps/online/nnet2/train_diag_ubm.sh: Training pass 2\n", "steps/online/nnet2/train_diag_ubm.sh: Training pass 3\n", "local/chain/run_ivector_common.sh: training the iVector extractor\n", "steps/online/nnet2/train_ivector_extractor.sh --cmd run.pl --nj 2 --ivector-dim 40 data/train exp/chain/diag_ubm exp/chain/extractor\n", "steps/online/nnet2/train_ivector_extractor.sh: Directory exp/chain/extractor already exists. Backing up iVector extractor in exp/chain/extractor/backup.YGm\n", "steps/online/nnet2/train_ivector_extractor.sh: doing Gaussian selection and posterior computation\n", "Accumulating stats (pass 0)\n", "Summing accs (pass 0)\n", "Updating model (pass 0)\n", "Accumulating stats (pass 1)\n", "Summing accs (pass 1)\n", "Updating model (pass 1)\n", "Accumulating stats (pass 2)\n", "Summing accs (pass 2)\n", "Updating model (pass 2)\n", "Accumulating stats (pass 3)\n", "Summing accs (pass 3)\n", "Updating model (pass 3)\n", "Accumulating stats (pass 4)\n", "Summing accs (pass 4)\n", "Updating model (pass 4)\n", "Accumulating stats (pass 5)\n", "Summing accs (pass 5)\n", "Updating model (pass 5)\n", "Accumulating stats (pass 6)\n", "Summing accs (pass 6)\n", "Updating model (pass 6)\n", "Accumulating stats (pass 7)\n", "Summing accs (pass 7)\n", "Updating model (pass 7)\n", "Accumulating stats (pass 8)\n", "Summing accs (pass 8)\n", "Updating model (pass 8)\n", "Accumulating stats (pass 9)\n", "Summing accs (pass 9)\n", "Updating model (pass 9)\n", "utils/data/modify_speaker_info.sh: copied data from data/train to exp/chain/ivectors_train/train_max2, number of speakers changed from 29 to 768\n", "utils/validate_data_dir.sh: Successfully validated data-directory exp/chain/ivectors_train/train_max2\n", "steps/online/nnet2/extract_ivectors_online.sh --cmd run.pl --nj 10 exp/chain/ivectors_train/train_max2 exp/chain/extractor exp/chain/ivectors_train\n", "steps/online/nnet2/extract_ivectors_online.sh: extracting iVectors\n", "steps/online/nnet2/extract_ivectors_online.sh: combining iVectors across jobs\n", "steps/online/nnet2/extract_ivectors_online.sh: done extracting (online) iVectors to exp/chain/ivectors_train using the extractor in exp/chain/extractor.\n", "local/chain/run_tdnn.sh: creating lang directory data/lang_chain with chain-type topology\n", "steps/align_fmllr_lats.sh --nj 20 --cmd run.pl data/train data/lang exp/tri3 exp/chain/tri3_train_lats\n", "steps/align_fmllr_lats.sh: feature type is lda\n", "steps/align_fmllr_lats.sh: compiling training graphs\n", "steps/align_fmllr_lats.sh: aligning data in data/train using exp/tri3/final.mdl and speaker-independent features.\n", "steps/align_fmllr_lats.sh: computing fMLLR transforms\n", "steps/align_fmllr_lats.sh: generating lattices containing alternate pronunciations.\n", "steps/align_fmllr_lats.sh: done generating lattices from training transcripts.\n", "44 warnings in exp/chain/tri3_train_lats/log/align_pass1.*.log\n", "20 warnings in exp/chain/tri3_train_lats/log/generate_lattices.*.log\n", "3 warnings in exp/chain/tri3_train_lats/log/fmllr.*.log\n", "steps/nnet3/chain/build_tree.sh --frame-subsampling-factor 3 --context-opts --context-width=2 --central-position=1 --cmd run.pl 2500 data/train data/lang_chain exp/tri3_ali exp/chain/tree\n", "steps/nnet3/chain/build_tree.sh: feature type is lda\n", "steps/nnet3/chain/build_tree.sh: Initializing monophone model (for alignment conversion, in case topology changed)\n", "steps/nnet3/chain/build_tree.sh: Accumulating tree stats\n", "steps/nnet3/chain/build_tree.sh: Getting questions for tree clustering.\n", "steps/nnet3/chain/build_tree.sh: Building the tree\n", "steps/nnet3/chain/build_tree.sh: Initializing the model\n", "steps/nnet3/chain/build_tree.sh: Converting alignments from exp/tri3_ali to use current tree\n", "steps/nnet3/chain/build_tree.sh: Done building tree\n", "local/chain/run_tdnn.sh: creating neural net configs using the xconfig parser\n", "tree-info exp/chain/tree/tree \n", "steps/nnet3/xconfig_to_configs.py --xconfig-file exp/chain/tdnn/configs/network.xconfig --config-dir exp/chain/tdnn/configs/\n", "nnet3-init exp/chain/tdnn/configs//ref.config exp/chain/tdnn/configs//ref.raw \n", "LOG (nnet3-init[5.5.1046~1-76cd5]:main():nnet3-init.cc:80) Initialized raw neural net and wrote it to exp/chain/tdnn/configs//ref.raw\n", "nnet3-info exp/chain/tdnn/configs//ref.raw \n", "nnet3-init exp/chain/tdnn/configs//ref.config exp/chain/tdnn/configs//ref.raw \n", "LOG (nnet3-init[5.5.1046~1-76cd5]:main():nnet3-init.cc:80) Initialized raw neural net and wrote it to exp/chain/tdnn/configs//ref.raw\n", "nnet3-info exp/chain/tdnn/configs//ref.raw \n", "2022-08-17 13:33:35,936 [steps/nnet3/chain/train.py:35 - <module> - INFO ] Starting chain model trainer (train.py)\n", "steps/nnet3/chain/train.py --use-gpu false --stage -10 --cmd run.pl --feat.online-ivector-dir exp/chain/ivectors_train --feat.cmvn-opts --norm-means=false --norm-vars=false --chain.xent-regularize 0.1 --chain.leaky-hmm-coefficient 0.1 --chain.l2-regularize 0.0 --chain.apply-deriv-weights false --chain.lm-opts=--num-extra-lm-states=2000 --egs.cmd run.pl --egs.dir  --egs.stage -10 --egs.opts --frames-overlap-per-eg 0 --constrained false --egs.chunk-width 140,100,160 --trainer.dropout-schedule 0,0@0.20,0.5@0.50,0 --trainer.add-option=--optimization.memory-compression-level=2 --trainer.num-chunk-per-minibatch 64 --trainer.frames-per-iter 2500000 --trainer.num-epochs 20 --trainer.optimization.num-jobs-initial 1 --trainer.optimization.num-jobs-final 1 --trainer.optimization.initial-effective-lrate 0.001 --trainer.optimization.final-effective-lrate 0.0001 --trainer.max-param-change 2.0 --cleanup.remove-egs true --feat-dir data/train --tree-dir exp/chain/tree --lat-dir exp/chain/tri3_train_lats --dir exp/chain/tdnn\n", "['steps/nnet3/chain/train.py', '--use-gpu', 'false', '--stage', '-10', '--cmd', 'run.pl', '--feat.online-ivector-dir', 'exp/chain/ivectors_train', '--feat.cmvn-opts', '--norm-means=false --norm-vars=false', '--chain.xent-regularize', '0.1', '--chain.leaky-hmm-coefficient', '0.1', '--chain.l2-regularize', '0.0', '--chain.apply-deriv-weights', 'false', '--chain.lm-opts=--num-extra-lm-states=2000', '--egs.cmd', 'run.pl', '--egs.dir', '', '--egs.stage', '-10', '--egs.opts', '--frames-overlap-per-eg 0 --constrained false', '--egs.chunk-width', '140,100,160', '--trainer.dropout-schedule', '0,0@0.20,0.5@0.50,0', '--trainer.add-option=--optimization.memory-compression-level=2', '--trainer.num-chunk-per-minibatch', '64', '--trainer.frames-per-iter', '2500000', '--trainer.num-epochs', '20', '--trainer.optimization.num-jobs-initial', '1', '--trainer.optimization.num-jobs-final', '1', '--trainer.optimization.initial-effective-lrate', '0.001', '--trainer.optimization.final-effective-lrate', '0.0001', '--trainer.max-param-change', '2.0', '--cleanup.remove-egs', 'true', '--feat-dir', 'data/train', '--tree-dir', 'exp/chain/tree', '--lat-dir', 'exp/chain/tri3_train_lats', '--dir', 'exp/chain/tdnn']\n", "2022-08-17 13:33:35,938 [steps/nnet3/chain/train.py:255 - process_args - WARNING ] Without using a GPU this will be very slow. nnet3 does not yet support multiple threads.\n", "2022-08-17 13:33:35,939 [steps/nnet3/chain/train.py:281 - train - INFO ] Arguments for the experiment\n", "{'alignment_subsampling_factor': 3,\n", " 'apply_deriv_weights': False,\n", " 'backstitch_training_interval': 1,\n", " 'backstitch_training_scale': 0.0,\n", " 'chunk_left_context': 0,\n", " 'chunk_left_context_initial': -1,\n", " 'chunk_right_context': 0,\n", " 'chunk_right_context_final': -1,\n", " 'chunk_width': '140,100,160',\n", " 'cleanup': True,\n", " 'cmvn_opts': '--norm-means=false --norm-vars=false',\n", " 'combine_sum_to_one_penalty': 0.0,\n", " 'command': 'run.pl',\n", " 'compute_per_dim_accuracy': False,\n", " 'deriv_truncate_margin': None,\n", " 'dir': 'exp/chain/tdnn',\n", " 'do_final_combination': True,\n", " 'dropout_schedule': '0,0@0.20,0.5@0.50,0',\n", " 'egs_command': 'run.pl',\n", " 'egs_dir': None,\n", " 'egs_nj': 0,\n", " 'egs_opts': '--frames-overlap-per-eg 0 --constrained false',\n", " 'egs_stage': -10,\n", " 'email': None,\n", " 'exit_stage': None,\n", " 'feat_dir': 'data/train',\n", " 'final_effective_lrate': 0.0001,\n", " 'frame_subsampling_factor': 3,\n", " 'frames_per_iter': 2500000,\n", " 'initial_effective_lrate': 0.001,\n", " 'input_model': None,\n", " 'l2_regularize': 0.0,\n", " 'lat_dir': 'exp/chain/tri3_train_lats',\n", " 'leaky_hmm_coefficient': 0.1,\n", " 'left_deriv_truncate': None,\n", " 'left_tolerance': 5,\n", " 'lm_opts': '--num-extra-lm-states=2000',\n", " 'max_lda_jobs': 10,\n", " 'max_models_combine': 20,\n", " 'max_objective_evaluations': 30,\n", " 'max_param_change': 2.0,\n", " 'momentum': 0.0,\n", " 'num_chunk_per_minibatch': '64',\n", " 'num_epochs': 20.0,\n", " 'num_jobs_final': 1,\n", " 'num_jobs_initial': 1,\n", " 'num_jobs_step': 1,\n", " 'online_ivector_dir': 'exp/chain/ivectors_train',\n", " 'preserve_model_interval': 100,\n", " 'presoftmax_prior_scale_power': -0.25,\n", " 'proportional_shrink': 0.0,\n", " 'rand_prune': 4.0,\n", " 'remove_egs': True,\n", " 'reporting_interval': 0.1,\n", " 'right_tolerance': 5,\n", " 'samples_per_iter': 400000,\n", " 'shrink_saturation_threshold': 0.4,\n", " 'shrink_value': 1.0,\n", " 'shuffle_buffer_size': 5000,\n", " 'srand': 0,\n", " 'stage': -10,\n", " 'train_opts': ['--optimization.memory-compression-level=2'],\n", " 'tree_dir': 'exp/chain/tree',\n", " 'use_gpu': 'no',\n", " 'xent_regularize': 0.1}\n", "2022-08-17 13:33:35,976 [steps/nnet3/chain/train.py:338 - train - INFO ] Creating phone language-model\n", "2022-08-17 13:33:36,333 [steps/nnet3/chain/train.py:343 - train - INFO ] Creating denominator FST\n", "copy-transition-model exp/chain/tree/final.mdl exp/chain/tdnn/0.trans_mdl \n", "LOG (copy-transition-model[5.5.1046~1-76cd5]:main():copy-transition-model.cc:62) Copied transition model.\n", "2022-08-17 13:33:36,951 [steps/nnet3/chain/train.py:379 - train - INFO ] Generating egs\n", "steps/nnet3/chain/get_egs.sh --frames-overlap-per-eg 0 --constrained false --cmd run.pl --cmvn-opts --norm-means=false --norm-vars=false --online-ivector-dir exp/chain/ivectors_train --left-context 27 --right-context 27 --left-context-initial -1 --right-context-final -1 --left-tolerance 5 --right-tolerance 5 --frame-subsampling-factor 3 --alignment-subsampling-factor 3 --stage -10 --frames-per-iter 2500000 --frames-per-eg 140,100,160 --srand 0 data/train exp/chain/tdnn exp/chain/tri3_train_lats exp/chain/tdnn/egs\n", "steps/nnet3/chain/get_egs.sh: Holding out 300 utterances in validation set and 300 in training diagnostic set, out of total 1519.\n", "steps/nnet3/chain/get_egs.sh: creating egs.  To ensure they are not deleted later you can do:  touch exp/chain/tdnn/egs/.nodelete\n", "steps/nnet3/chain/get_egs.sh: feature type is raw, with 'apply-cmvn'\n", "tree-info exp/chain/tdnn/tree \n", "feat-to-dim scp:exp/chain/ivectors_train/ivector_online.scp - \n", "steps/nnet3/chain/get_egs.sh: working out number of frames of training data\n", "steps/nnet3/chain/get_egs.sh: working out feature dim\n", "steps/nnet3/chain/get_egs.sh: creating 1 archives, each with 11949 egs, with\n", "steps/nnet3/chain/get_egs.sh:   140,100,160 labels per example, and (left,right) context = (27,27)\n", "steps/nnet3/chain/get_egs.sh: Getting validation and training subset examples in background.\n", "steps/nnet3/chain/get_egs.sh: Generating training examples on disk\n", "steps/nnet3/chain/get_egs.sh: recombining and shuffling order of archives on disk\n", "steps/nnet3/chain/get_egs.sh: Getting subsets of validation examples for diagnostics and combination.\n", "steps/nnet3/chain/get_egs.sh: Removing temporary archives, alignments and lattices\n", "steps/nnet3/chain/get_egs.sh: Finished preparing training examples\n", "2022-08-17 13:34:13,249 [steps/nnet3/chain/train.py:428 - train - INFO ] Copying the properties from exp/chain/tdnn/egs to exp/chain/tdnn\n", "2022-08-17 13:34:13,249 [steps/nnet3/chain/train.py:451 - train - INFO ] Preparing the initial acoustic model.\n", "2022-08-17 13:34:13,511 [steps/nnet3/chain/train.py:485 - train - INFO ] Training will run for 20.0 epochs = 60 iterations\n", "2022-08-17 13:34:13,511 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 0/59   Jobs: 1   Epoch: 0.00/20.0 (0.0% complete)   lr: 0.001000   \n", "2022-08-17 13:45:06,298 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 1/59   Jobs: 1   Epoch: 0.33/20.0 (1.7% complete)   lr: 0.000962   \n", "2022-08-17 13:57:11,651 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 2/59   Jobs: 1   Epoch: 0.67/20.0 (3.3% complete)   lr: 0.000926   \n", "2022-08-17 14:09:18,884 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 3/59   Jobs: 1   Epoch: 1.00/20.0 (5.0% complete)   lr: 0.000891   \n", "2022-08-17 14:21:18,325 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 4/59   Jobs: 1   Epoch: 1.33/20.0 (6.7% complete)   lr: 0.000858   \n", "2022-08-17 14:33:01,155 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 5/59   Jobs: 1   Epoch: 1.67/20.0 (8.3% complete)   lr: 0.000825   \n", "2022-08-17 14:45:02,351 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 6/59   Jobs: 1   Epoch: 2.00/20.0 (10.0% complete)   lr: 0.000794   \n", "2022-08-17 14:57:33,705 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 7/59   Jobs: 1   Epoch: 2.33/20.0 (11.7% complete)   lr: 0.000764   \n", "2022-08-17 15:10:01,021 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 8/59   Jobs: 1   Epoch: 2.67/20.0 (13.3% complete)   lr: 0.000736   \n", "2022-08-17 15:22:10,748 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 9/59   Jobs: 1   Epoch: 3.00/20.0 (15.0% complete)   lr: 0.000708   \n", "2022-08-17 15:34:09,008 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 10/59   Jobs: 1   Epoch: 3.33/20.0 (16.7% complete)   lr: 0.000681   \n", "2022-08-17 15:45:51,448 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 11/59   Jobs: 1   Epoch: 3.67/20.0 (18.3% complete)   lr: 0.000656   \n", "2022-08-17 15:57:34,728 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 12/59   Jobs: 1   Epoch: 4.00/20.0 (20.0% complete)   lr: 0.000631   \n", "2022-08-17 16:09:19,257 [steps/nnet3/chain/train.py:529 - train - INFO ] Iter: 13/59   Jobs: 1   Epoch: 4.33/20.0 (21.7% complete)   lr: 0.000607   \n", "2022-08-17 16:15:40,558 [steps/libs/common.py:236 - background_command_waiter - ERROR ] Command exited with status -2: run.pl  exp/chain/tdnn/log/train.13.1.log                     nnet3-chain-train --use-gpu=no                      --apply-deriv-weights=False                     --l2-regularize=0.0 --leaky-hmm-coefficient=0.1                     --read-cache=exp/chain/tdnn/cache.13 --write-cache=exp/chain/tdnn/cache.14  --xent-regularize=0.1                                          --print-interval=10 --momentum=0.0                     --max-param-change=2.0                     --backstitch-training-scale=0.0                     --backstitch-training-interval=1                     --l2-regularize-factor=1.0 --optimization.memory-compression-level=2                     --srand=13                     \"nnet3-am-copy --raw=true --learning-rate=0.000607202195691 --scale=1.0 exp/chain/tdnn/13.mdl - |nnet3-copy --edits='set-dropout-proportion name=* proportion=0.0277777777778' - - |\" exp/chain/tdnn/den.fst                     \"ark,bg:nnet3-chain-copy-egs                          --frame-shift=2                         ark:exp/chain/tdnn/egs/cegs.1.ark ark:- |                         nnet3-chain-shuffle-egs --buffer-size=5000                         --srand=13 ark:- ark:- | nnet3-chain-merge-egs                         --minibatch-size=64 ark:- ark:- |\"                     exp/chain/tdnn/14.1.raw\n"]}]}, {"cell_type": "code", "source": ["!cp exp/chain/tdnn/13.mdl exp/chain/tdnn/final.mdl"], "metadata": {"id": "_6cOADJBluSm"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["!bash run.sh --stage 5"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "La4qqC-kl9lf", "outputId": "3d0cc930-43a2-4ec9-81b9-8436fed8669f"}, "execution_count": 23, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Converting 'data/local/lm/lm_tgsmall.arpa.gz' to FST\n", "arpa2fst --disambig-symbol=#0 --read-symbol-table=data/lang_test/words.txt - data/lang_test/G.fst \n", "LOG (arpa2fst[5.5.1046~1-76cd5]:Read():arpa-file-parser.cc:94) Reading \\data\\ section.\n", "LOG (arpa2fst[5.5.1046~1-76cd5]:Read():arpa-file-parser.cc:149) Reading \\1-grams: section.\n", "LOG (arpa2fst[5.5.1046~1-76cd5]:Read():arpa-file-parser.cc:149) Reading \\2-grams: section.\n", "LOG (arpa2fst[5.5.1046~1-76cd5]:Read():arpa-file-parser.cc:149) Reading \\3-grams: section.\n", "LOG (arpa2fst[5.5.1046~1-76cd5]:RemoveRedundantStates():arpa-lm-compiler.cc:359) Reduced num-states from 1183418 to 249533\n", "fstisstochastic data/lang_test/G.fst \n", "9.90926e-06 -0.29805\n", "Succeeded in formatting LM: 'data/local/lm/lm_tgsmall.arpa.gz'\n", "tree-info exp/chain/tdnn/tree \n", "tree-info exp/chain/tdnn/tree \n", "fstdeterminizestar --use-log=true \n", "fsttablecompose data/lang_test/L_disambig.fst data/lang_test/G.fst \n", "fstpushspecial \n", "fstminimizeencoded \n", "fstisstochastic data/lang_test/tmp/LG.fst \n", "-0.0577829 -0.0586653\n", "[info]: LG not stochastic.\n", "fstcomposecontext --context-size=2 --central-position=1 --read-disambig-syms=data/lang_test/phones/disambig.int --write-disambig-syms=data/lang_test/tmp/disambig_ilabels_2_1.int data/lang_test/tmp/ilabels_2_1.339337 data/lang_test/tmp/LG.fst \n", "fstisstochastic data/lang_test/tmp/CLG_2_1.fst \n", "-0.0577829 -0.0586653\n", "[info]: CLG not stochastic.\n", "make-h-transducer --disambig-syms-out=exp/chain/tdnn/graph/disambig_tid.int --transition-scale=1.0 data/lang_test/tmp/ilabels_2_1 exp/chain/tdnn/tree exp/chain/tdnn/final.mdl \n", "fsttablecompose exp/chain/tdnn/graph/Ha.fst data/lang_test/tmp/CLG_2_1.fst \n", "fstdeterminizestar --use-log=true \n", "fstrmepslocal \n", "fstrmsymbols exp/chain/tdnn/graph/disambig_tid.int \n", "fstminimizeencoded \n", "fstisstochastic exp/chain/tdnn/graph/HCLGa.fst \n", "-0.019117 -0.213417\n", "HCLGa is not stochastic\n", "add-self-loops --self-loop-scale=1.0 --reorder=true exp/chain/tdnn/final.mdl exp/chain/tdnn/graph/HCLGa.fst \n", "fstisstochastic exp/chain/tdnn/graph/HCLG.fst \n", "1.90465e-09 -0.146711\n", "[info]: final HCLG is not stochastic.\n", "arpa-to-const-arpa --bos-symbol=200004 --eos-symbol=200005 --unk-symbol=2 'gunzip -c data/local/lm/lm_tgmed.arpa.gz | utils/map_arpa_lm.pl data/lang_test_rescore/words.txt|' data/lang_test_rescore/G.carpa \n", "LOG (arpa-to-const-arpa[5.5.1046~1-76cd5]:BuildConstArpaLm():const-arpa-lm.cc:1078) Reading gunzip -c data/local/lm/lm_tgmed.arpa.gz | utils/map_arpa_lm.pl data/lang_test_rescore/words.txt|\n", "utils/map_arpa_lm.pl: Processing \"\\data\\\"\n", "utils/map_arpa_lm.pl: Processing \"\\1-grams:\\\"\n", "LOG (arpa-to-const-arpa[5.5.1046~1-76cd5]:Read():arpa-file-parser.cc:94) Reading \\data\\ section.\n", "LOG (arpa-to-const-arpa[5.5.1046~1-76cd5]:Read():arpa-file-parser.cc:149) Reading \\1-grams: section.\n", "utils/map_arpa_lm.pl: Processing \"\\2-grams:\\\"\n", "LOG (arpa-to-const-arpa[5.5.1046~1-76cd5]:Read():arpa-file-parser.cc:149) Reading \\2-grams: section.\n", "utils/map_arpa_lm.pl: Processing \"\\3-grams:\\\"\n", "LOG (arpa-to-const-arpa[5.5.1046~1-76cd5]:Read():arpa-file-parser.cc:149) Reading \\3-grams: section.\n", "steps/make_mfcc.sh --cmd run.pl --nj 2 data/test exp/make_mfcc/test\n", "steps/make_mfcc.sh: moving data/test/feats.scp to data/test/.backup\n", "utils/validate_data_dir.sh: Successfully validated data-directory data/test\n", "steps/make_mfcc.sh: [info]: no segments file exists: assuming wav.scp indexed by utterance.\n", "steps/make_mfcc.sh: Succeeded creating MFCC features for test\n", "steps/compute_cmvn_stats.sh data/test exp/make_mfcc/test\n", "Succeeded creating CMVN stats for test\n", "steps/online/nnet2/extract_ivectors_online.sh --nj 2 data/test exp/chain/extractor exp/chain/ivectors_test\n", "steps/online/nnet2/extract_ivectors_online.sh: extracting iVectors\n", "steps/online/nnet2/extract_ivectors_online.sh: combining iVectors across jobs\n", "steps/online/nnet2/extract_ivectors_online.sh: done extracting (online) iVectors to exp/chain/ivectors_test using the extractor in exp/chain/extractor.\n", "steps/nnet3/decode.sh --cmd run.pl --num-threads 10 --nj 1 --beam 13.0 --max-active 7000 --lattice-beam 4.0 --online-ivector-dir exp/chain/ivectors_test --acwt 1.0 --post-decode-acwt 10.0 exp/chain/tdnn/graph data/test exp/chain/tdnn/decode_test\n", "steps/nnet3/decode.sh: feature type is raw\n", "steps/diagnostic/analyze_lats.sh --cmd run.pl --iter final exp/chain/tdnn/graph exp/chain/tdnn/decode_test\n", "steps/diagnostic/analyze_lats.sh: see stats in exp/chain/tdnn/decode_test/log/analyze_alignments.log\n", "Overall, lattice depth (10,50,90-percentile)=(1,3,17) and mean=6.9\n", "steps/diagnostic/analyze_lats.sh: see stats in exp/chain/tdnn/decode_test/log/analyze_lattice_depth_stats.log\n", "score best paths\n", "local/score.sh --cmd run.pl data/test exp/chain/tdnn/graph exp/chain/tdnn/decode_test\n", "local/score.sh: scoring with word insertion penalty=0.0\n", "score confidence and timing with sclite\n", "Decoding done.\n", "steps/lmrescore_const_arpa.sh data/lang_test data/lang_test_rescore data/test exp/chain/tdnn/decode_test exp/chain/tdnn/decode_test_rescore\n", "local/score.sh --cmd run.pl data/test data/lang_test_rescore exp/chain/tdnn/decode_test_rescore\n", "local/score.sh: scoring with word insertion penalty=0.0\n", "%WER 22.68 [ 4568 / 20138, 254 ins, 945 del, 3369 sub ] exp/chain/tdnn/decode_test/wer_11_0.0\n", "%WER 20.24 [ 4076 / 20138, 263 ins, 771 del, 3042 sub ] exp/chain/tdnn/decode_test_rescore/wer_11_0.0\n"]}]}]}