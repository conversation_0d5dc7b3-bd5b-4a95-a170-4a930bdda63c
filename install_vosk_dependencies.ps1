# Vosk CGO Dependencies Installation Script for Windows
# Run this script as Administrator

Write-Host "Installing Vosk CGO Dependencies for Windows" -ForegroundColor Green
Write-Host "=" * 50

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "ERROR: This script must be run as Administrator" -ForegroundColor Red
    Write-Host "TIP: Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Step 1: Install Chocolatey (if not already installed)
Write-Host "Checking for Chocolatey..." -ForegroundColor Cyan
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
} else {
    Write-Host "SUCCESS: Chocolatey already installed" -ForegroundColor Green
}

# Step 2: Install MinGW-w64 (GCC compiler)
Write-Host "Checking for GCC compiler..." -ForegroundColor Cyan
if (!(Get-Command gcc -ErrorAction SilentlyContinue)) {
    Write-Host "Installing MinGW-w64..." -ForegroundColor Yellow
    choco install mingw -y

    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
} else {
    Write-Host "SUCCESS: GCC compiler already installed" -ForegroundColor Green
}

# Step 3: Install Git (if not already installed)
Write-Host "Checking for Git..." -ForegroundColor Cyan
if (!(Get-Command git -ErrorAction SilentlyContinue)) {
    Write-Host "Installing Git..." -ForegroundColor Yellow
    choco install git -y
} else {
    Write-Host "SUCCESS: Git already installed" -ForegroundColor Green
}

# Step 4: Download Vosk C Libraries
Write-Host "Downloading Vosk C Libraries..." -ForegroundColor Yellow
$voskVersion = "0.3.50"
$voskUrl = "https://github.com/alphacep/vosk-api/releases/download/v$voskVersion/vosk-win64-$voskVersion.zip"
$voskDir = "C:\vosk"
$voskZip = "$env:TEMP\vosk-win64.zip"

# Create Vosk directory
if (!(Test-Path $voskDir)) {
    New-Item -ItemType Directory -Path $voskDir -Force | Out-Null
}

# Download Vosk libraries
Write-Host "Downloading from: $voskUrl" -ForegroundColor Cyan
try {
    Invoke-WebRequest -Uri $voskUrl -OutFile $voskZip -UseBasicParsing
    Write-Host "SUCCESS: Vosk libraries downloaded" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to download Vosk libraries: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "TIP: Please download manually from: https://github.com/alphacep/vosk-api/releases" -ForegroundColor Yellow
    exit 1
}

# Extract Vosk libraries
Write-Host "Extracting Vosk libraries to $voskDir..." -ForegroundColor Yellow
try {
    Expand-Archive -Path $voskZip -DestinationPath $voskDir -Force
    Write-Host "SUCCESS: Vosk libraries extracted" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to extract Vosk libraries: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 5: Set Environment Variables
Write-Host "Setting environment variables..." -ForegroundColor Yellow

# Set VOSK_ROOT
[Environment]::SetEnvironmentVariable("VOSK_ROOT", $voskDir, "Machine")

# Set CGO flags
$cgoFlags = "-I$voskDir\include"
$ldFlags = "-L$voskDir\lib -lvosk"

[Environment]::SetEnvironmentVariable("CGO_CFLAGS", $cgoFlags, "Machine")
[Environment]::SetEnvironmentVariable("CGO_LDFLAGS", $ldFlags, "Machine")

Write-Host "SUCCESS: Environment variables set:" -ForegroundColor Green
Write-Host "   VOSK_ROOT = $voskDir" -ForegroundColor Cyan
Write-Host "   CGO_CFLAGS = $cgoFlags" -ForegroundColor Cyan
Write-Host "   CGO_LDFLAGS = $ldFlags" -ForegroundColor Cyan

# Step 6: Verify Installation
Write-Host "Verifying installation..." -ForegroundColor Cyan

# Check GCC
if (Get-Command gcc -ErrorAction SilentlyContinue) {
    $gccVersion = gcc --version | Select-Object -First 1
    Write-Host "SUCCESS: GCC: $gccVersion" -ForegroundColor Green
} else {
    Write-Host "ERROR: GCC not found in PATH" -ForegroundColor Red
}

# Check Vosk files
$voskLib = Join-Path $voskDir "lib\vosk.lib"
$voskHeader = Join-Path $voskDir "include\vosk_api.h"

if (Test-Path $voskLib) {
    Write-Host "SUCCESS: Vosk library found: $voskLib" -ForegroundColor Green
} else {
    Write-Host "ERROR: Vosk library not found: $voskLib" -ForegroundColor Red
}

if (Test-Path $voskHeader) {
    Write-Host "SUCCESS: Vosk headers found: $voskHeader" -ForegroundColor Green
} else {
    Write-Host "ERROR: Vosk headers not found: $voskHeader" -ForegroundColor Red
}

# Cleanup
Remove-Item $voskZip -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "🎉 Installation completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Close and reopen your PowerShell/Command Prompt" -ForegroundColor White
Write-Host "2. Navigate to your Go project directory" -ForegroundColor White
Write-Host "3. Run: go mod tidy" -ForegroundColor White
Write-Host "4. Run: go build speech_recorder_vosk_corrected.go" -ForegroundColor White
Write-Host ""
Write-Host "💡 If you encounter issues, check:" -ForegroundColor Yellow
Write-Host "   - Environment variables are set correctly" -ForegroundColor White
Write-Host "   - GCC is in your PATH" -ForegroundColor White
Write-Host "   - Vosk model files are downloaded" -ForegroundColor White
